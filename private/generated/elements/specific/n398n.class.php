<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Subpagelinkcontainer
class n398n extends \nl\actinum\custom\elements\generic\Subpagelinkcontainer {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n398n';
    public static $parentElementid = 'n311n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n4912n',
  1 => 'n4914n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n398n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n398n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>