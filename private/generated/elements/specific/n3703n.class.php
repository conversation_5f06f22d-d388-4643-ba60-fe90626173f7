<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockcontainer
class n3703n extends \nl\actinum\custom\elements\generic\Blockcontainer {

    
    public static $additionalcssclasses = NULL;
    public static $style = '._this_{width:25%;}';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3703n';
    public static $parentElementid = 'n3689n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n7508n',
  1 => 'n5799n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3703n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3703n'] =
<<<'EOD'
.n3703n{
  width: 25%;
}

EOD;
        return $result;
    }


}
}
?>