<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockcontainer
class n3378n extends \nl\actinum\custom\elements\generic\Blockcontainer {

    
    public static $additionalcssclasses = NULL;
    public static $style = '._this_ {
  width: 25%;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3378n';
    public static $parentElementid = 'n3357n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n6538n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3378n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3378n'] =
<<<'EOD'
.n3378n{
  width: 25%;
}

EOD;
        return $result;
    }


}
}
?>