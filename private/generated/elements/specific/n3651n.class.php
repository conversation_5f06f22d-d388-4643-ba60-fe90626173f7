<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridcolumnint
class n3651n extends \nl\actinum\custom\elements\generic\Gridcolumnint {

    
    public static $dbfield = 'factor';
    public static $addlink = false;
    public static $disabledadvancedsearch = NULL;
    public static $title = 'Factor';
    public static $show = true;
    public static $dbtrigger = NULL;
    public static $mappedforminput = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3651n';
    public static $parentElementid = 'n3643n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3651n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3651n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>