<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Subpagegroup
class n3356n extends \nl\actinum\custom\elements\generic\Subpagegroup {

    
    public static $title = 'Default';
    public static $elementid = 'n3356n';
    public static $parentElementid = 'n3353n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n3357n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3356n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3356n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>