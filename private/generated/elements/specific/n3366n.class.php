<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputcontainer
class n3366n extends \nl\actinum\custom\elements\generic\Forminputcontainer {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3366n';
    public static $parentElementid = 'n3362n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n3367n',
  1 => 'n3368n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3366n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3366n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>