<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockcontainer
class n4785n extends \nl\actinum\custom\elements\generic\Blockcontainer {

    
    public static $additionalcssclasses = NULL;
    public static $style = '._this_{width:75%; padding-right:30px;}';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4785n';
    public static $parentElementid = 'n4784n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n4786n',
  1 => 'n4787n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4785n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4785n'] =
<<<'EOD'
.n4785n{
  width: 75%;  padding-right: 30px;
}

EOD;
        return $result;
    }


}
}
?>