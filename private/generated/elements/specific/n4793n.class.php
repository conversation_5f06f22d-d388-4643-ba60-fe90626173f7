<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockcontainer
class n4793n extends \nl\actinum\custom\elements\generic\Blockcontainer {

    
    public static $additionalcssclasses = NULL;
    public static $style = '._this_ {
  width: 25%;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4793n';
    public static $parentElementid = 'n4784n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4793n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4793n'] =
<<<'EOD'
.n4793n{
  width: 25%;
}

EOD;
        return $result;
    }


}
}
?>