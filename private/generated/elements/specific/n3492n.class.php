<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockcontainer
class n3492n extends \nl\actinum\custom\elements\generic\Blockcontainer {

    
    public static $additionalcssclasses = NULL;
    public static $style = '._this_{width:75%; padding-right:30px;}';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3492n';
    public static $parentElementid = 'n3491n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n3493n',
  1 => 'n3496n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3492n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3492n'] =
<<<'EOD'
.n3492n{
  width: 75%;  padding-right: 30px;
}

EOD;
        return $result;
    }


}
}
?>