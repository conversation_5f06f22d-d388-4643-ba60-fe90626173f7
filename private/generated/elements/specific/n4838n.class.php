<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridroweditlink
class n4838n extends \nl\actinum\custom\elements\generic\Gridroweditlink {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4838n';
    public static $parentElementid = 'n4832n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4838n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4838n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>