<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridrowform
class n3468n extends \nl\actinum\custom\elements\generic\Gridrowform {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3468n';
    public static $parentElementid = 'n3466n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n3469n',
  1 => 'n7799n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3468n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3468n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>