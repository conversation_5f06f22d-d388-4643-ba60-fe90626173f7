<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputcontainer
class n3566n extends \nl\actinum\custom\elements\generic\Forminputcontainer {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3566n';
    public static $parentElementid = 'n3565n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n3568n',
  1 => 'n3569n',
  2 => 'n3570n',
  3 => 'n3573n',
  4 => 'n3574n',
  5 => 'n3575n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3566n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3566n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>