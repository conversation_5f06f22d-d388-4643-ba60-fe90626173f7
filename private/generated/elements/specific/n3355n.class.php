<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: State
class n3355n extends \nl\actinum\custom\elements\generic\State {

    
    public static $add = 'n3356n';
    public static $edit = 'n3356n';
    public static $view = 'n3356n';
    public static $initialstate = true;
    public static $x = 100;
    public static $y = 100;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3355n';
    public static $parentElementid = 'n3354n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3355n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3355n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>