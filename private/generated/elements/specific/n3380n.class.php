<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Buttonedit
class n3380n extends \nl\actinum\custom\elements\generic\Buttonedit {

    
    public static $afterclickgotopage = 'n3353n';
    public static $afterclickgotomode = 'view';
    public static $afterclickgotorecord = 'currentrecord';
    public static $title = 'Bewerken';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3380n';
    public static $parentElementid = 'n3359n';
    public static $visibleonadd = false;
    public static $visibleonedit = false;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3380n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3380n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>