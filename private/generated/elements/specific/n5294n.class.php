<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Subpagegroup
class n5294n extends \nl\actinum\custom\elements\generic\Subpagegroup {

    
    public static $title = 'Default';
    public static $elementid = 'n5294n';
    public static $parentElementid = 'n5293n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n5295n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n5294n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n5294n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>