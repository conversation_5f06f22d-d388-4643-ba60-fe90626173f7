<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Header3
class n4187n extends \nl\actinum\custom\elements\generic\Header3 {

    
    public static $title = 'Inspectierondestatus';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4187n';
    public static $parentElementid = 'n4186n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4187n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4187n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>