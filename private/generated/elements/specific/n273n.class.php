<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Block
class n273n extends \nl\actinum\custom\elements\generic\Block {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n273n';
    public static $parentElementid = 'n271n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n2650n',
  1 => 'n274n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n273n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n273n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>