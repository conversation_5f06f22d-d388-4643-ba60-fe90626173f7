<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputstring
class n4945n extends \nl\actinum\custom\elements\generic\Forminputstring {

    
    public static $subtitle = 'Vraag';
    public static $showsubtitle = true;
    public static $dbfield = 'question';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = true;
    public static $style = '._this_ {
  width: 540px;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4945n';
    public static $parentElementid = 'n4937n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4945n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4945n'] =
<<<'EOD'
.n4945n{
  width: 540px;
}

EOD;
        return $result;
    }


}
}
?>