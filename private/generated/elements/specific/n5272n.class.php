<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockcontainer
class n5272n extends \nl\actinum\custom\elements\generic\Blockcontainer {

    
    public static $additionalcssclasses = NULL;
    public static $style = '._this_{width:25%;}';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n5272n';
    public static $parentElementid = 'n5258n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n5348n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n5272n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n5272n'] =
<<<'EOD'
.n5272n{
  width: 25%;
}

EOD;
        return $result;
    }


}
}
?>