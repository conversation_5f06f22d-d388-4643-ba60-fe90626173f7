<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Block
class n4768n extends \nl\actinum\custom\elements\generic\Block {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4768n';
    public static $parentElementid = 'n4766n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n4769n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4768n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4768n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>