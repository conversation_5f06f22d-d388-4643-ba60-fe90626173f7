<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockcontainer
class n3712n extends \nl\actinum\custom\elements\generic\Blockcontainer {

    
    public static $additionalcssclasses = NULL;
    public static $style = '._this_{width:75%; padding-right:30px;}';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3712n';
    public static $parentElementid = 'n3711n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n3713n',
  1 => 'n4215n',
  2 => 'n5707n',
  3 => 'n4216n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3712n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3712n'] =
<<<'EOD'
.n3712n{
  width: 75%;  padding-right: 30px;
}

EOD;
        return $result;
    }


}
}
?>