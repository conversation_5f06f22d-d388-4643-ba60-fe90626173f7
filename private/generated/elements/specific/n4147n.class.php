<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Grid
class n4147n extends \nl\actinum\custom\elements\generic\Grid {

    
    public static $detailpage = 'n3898n';
    public static $tablename = 'audit';
    public static $dbfield = NULL;
    public static $rowtypetopagemapping = 'null';
    public static $sortable = NULL;
    public static $paginate = true;
    public static $itemsperpage = 10;
    public static $hideitemsperpagelist = NULL;
    public static $defaultentries = 'null';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4147n';
    public static $parentElementid = 'n4141n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n4148n',
  1 => 'n4201n',
  2 => 'n4160n',
  3 => 'n4159n',
  4 => 'n7728n',
  5 => 'n4200n',
  6 => 'n4173n',
  7 => 'n7426n',
  8 => 'n4152n',
  9 => 'n4153n',
  10 => 'n4551n',
  11 => 'n6539n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4147n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4147n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>