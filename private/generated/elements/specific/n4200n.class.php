<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridcolumnstring
class n4200n extends \nl\actinum\custom\elements\generic\Gridcolumnstring {

    
    public static $dbfield = 'number';
    public static $addlink = false;
    public static $disabledadvancedsearch = NULL;
    public static $title = 'Rondenummer';
    public static $show = false;
    public static $dbtrigger = NULL;
    public static $mappedforminput = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4200n';
    public static $parentElementid = 'n4147n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4200n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4200n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>