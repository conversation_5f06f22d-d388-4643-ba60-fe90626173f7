<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridcolumndropdown
class n3652n extends \nl\actinum\custom\elements\generic\Gridcolumndropdown {

    
    public static $tablename = 'answertype';
    public static $selectdql = 'answertype.name';
    public static $aliaspostfix = NULL;
    public static $dbfield = 'answertype';
    public static $addlink = false;
    public static $disabledadvancedsearch = NULL;
    public static $title = 'Type antwoord';
    public static $show = true;
    public static $dbtrigger = NULL;
    public static $mappedforminput = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3652n';
    public static $parentElementid = 'n3643n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3652n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3652n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>