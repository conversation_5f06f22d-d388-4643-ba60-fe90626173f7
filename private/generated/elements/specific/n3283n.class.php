<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputtext
class n3283n extends \nl\actinum\custom\elements\generic\Forminputtext {

    
    public static $subtitle = 'Omschrijving';
    public static $showsubtitle = NULL;
    public static $dbfield = 'description';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = '._this_ {
  width: 540px;
}
._this_ textarea {
  height: 50px;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3283n';
    public static $parentElementid = 'n3281n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3283n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3283n'] =
<<<'EOD'
.n3283n{
  width: 540px;
}
.n3283n textarea{
  height: 50px;
}

EOD;
        return $result;
    }


}
}
?>