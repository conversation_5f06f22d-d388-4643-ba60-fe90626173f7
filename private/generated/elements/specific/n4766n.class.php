<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockcontainer
class n4766n extends \nl\actinum\custom\elements\generic\Blockcontainer {

    
    public static $additionalcssclasses = NULL;
    public static $style = '._this_{width:75%; padding-right:30px;}';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4766n';
    public static $parentElementid = 'n4765n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n4767n',
  1 => 'n8787n',
  2 => 'n4768n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4766n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4766n'] =
<<<'EOD'
.n4766n{
  width: 75%;  padding-right: 30px;
}

EOD;
        return $result;
    }


}
}
?>