<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Buttonsubmit
class n288n extends \nl\actinum\custom\elements\generic\Buttonsubmit {

    
    public static $afterclickgotopage = 'n266n';
    public static $afterclickgotomode = 'view';
    public static $afterclickgotorecord = 'currentrecord';
    public static $title = 'Opslaan';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n288n';
    public static $parentElementid = 'n272n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = false;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n288n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n288n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>