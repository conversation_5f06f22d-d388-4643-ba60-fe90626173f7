<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockpopup
class n4216n extends \nl\actinum\custom\elements\generic\Blockpopup {

    
    public static $title = 'Inspectieronde plannen...';
    public static $style = '._this_ {
  width: 700px;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4216n';
    public static $parentElementid = 'n3712n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n10328n',
  1 => 'n4224n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4216n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4216n'] =
<<<'EOD'
.n4216n{
  width: 700px;
}

EOD;
        return $result;
    }


}
}
?>