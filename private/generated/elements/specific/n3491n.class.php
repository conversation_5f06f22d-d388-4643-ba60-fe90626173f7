<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Subpage
class n3491n extends \nl\actinum\custom\elements\generic\Subpage {

    
    public static $title = 'Instellingen';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3491n';
    public static $parentElementid = 'n3490n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n3492n',
  1 => 'n3538n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3491n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3491n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>