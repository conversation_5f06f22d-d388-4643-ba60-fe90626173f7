<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputcontainer
class n4971n extends \nl\actinum\custom\elements\generic\Forminputcontainer {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4971n';
    public static $parentElementid = 'n4970n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n4972n',
  1 => 'n4973n',
  2 => 'n4974n',
  3 => 'n4975n',
  4 => 'n4976n',
  5 => 'n4977n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4971n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4971n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>