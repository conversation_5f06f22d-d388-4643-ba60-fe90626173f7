<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Plusbuttongrid
class n3541n extends \nl\actinum\custom\elements\generic\Plusbuttongrid {

    
    public static $detailpage = NULL;
    public static $rowtypetopagemapping = 'null';
    public static $dbfield = 'questionansweroptions';
    public static $tablename = 'questionansweroption';
    public static $rowsdraggable = true;
    public static $removelinkcaption = 'Remove';
    public static $addlinkcaption = 'Add';
    public static $min = '1';
    public static $max = '0';
    public static $defaultentries = 'null';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3541n';
    public static $parentElementid = 'n3580n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n3576n',
  1 => 'n3577n',
  2 => 'n6299n',
  3 => 'n3578n',
  4 => 'n3579n',
  5 => 'n3565n',
  6 => 'n3571n',
  7 => 'n3572n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3541n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3541n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>