<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridaddoncontainer
class n4858n extends \nl\actinum\custom\elements\generic\Gridaddoncontainer {

    
    public static $grid = 'n4857n';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4858n';
    public static $parentElementid = 'n4857n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n4859n',
  1 => 'n4861n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4858n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4858n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>