<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blocktab
class n4769n extends \nl\actinum\custom\elements\generic\Blocktab {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4769n';
    public static $parentElementid = 'n4768n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n4770n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4769n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4769n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>