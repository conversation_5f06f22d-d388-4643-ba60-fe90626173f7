<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Blockcontainer
class n3580n extends \nl\actinum\custom\elements\generic\Blockcontainer {

    
    public static $additionalcssclasses = NULL;
    public static $style = '._this_ {
  width: 100%;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n3580n';
    public static $parentElementid = 'n3358n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n3541n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n3580n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n3580n'] =
<<<'EOD'
.n3580n{
  width: 100%;
}

EOD;
        return $result;
    }


}
}
?>