<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: State
class n4744n extends \nl\actinum\custom\elements\generic\State {

    
    public static $add = 'n4745n';
    public static $edit = 'n4745n';
    public static $view = 'n4745n';
    public static $initialstate = true;
    public static $x = 100;
    public static $y = 100;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4744n';
    public static $parentElementid = 'n4743n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n4744n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n4744n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>