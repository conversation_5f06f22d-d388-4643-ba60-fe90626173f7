<?php
namespace nl\actinum\generated\tables{

class action extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
  0 => 'n19452n',
  1 => 'n5265n',
  2 => 'n17681n',
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = true;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
  0 => 'n19515n',
  1 => 'n20064n',
  2 => 'n4500n',
  3 => 'n17597n',
);


        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $number = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $action = '';

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $remark = '';

        
        /**
*@ManyToOne(targetEntity="employee")
        */

        
        protected $employee = NULL;

        
        /**
*@ManyToOne(targetEntity="department")
        */

        
        protected $department = NULL;

        
        /**
*@Column(type="date", nullable=true)
        */

        
        protected $dateactioncreated = NULL;

        
        /**
*@Column(type="date", nullable=true)
        */

        
        protected $dateactionplanned = NULL;

        
        /**
*@Column(type="date", nullable=true)
        */

        
        protected $dateactiondeadline = NULL;

        
        /**
*@Column(type="date", nullable=true)
        */

        
        protected $dateactionresolved = NULL;

        
        /**
*@Column(type="date", nullable=true)
        */

        
        protected $dateactionverified = NULL;

        
        /**
*@ManyToOne(targetEntity="actiontype")
        */

        
        protected $actiontype = NULL;

        
        /**
*@ManyToOne(targetEntity="actioncategory")
        */

        
        protected $actioncategory = NULL;

        
        /**
*@ManyToOne(targetEntity="priority")
        */

        
        protected $priority = NULL;

        
        /**
*@ManyToOne(targetEntity="nonconformity")
        */

        
        protected $nonconformity = NULL;

        
        /**
*@ManyToOne(targetEntity="memo")
        */

        
        protected $memo = NULL;

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $vrijveld1 = '';

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $vrijveld2 = '';

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $vrijveld3 = '';

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $vrijveld1dropdownacties_id = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $vrijveld2dropdownacties_id = NULL;

        
        /**
*@ManyToOne(targetEntity="vrijveld1dropdownacties")
        */

        
        protected $vrijveld1dropdownacties = NULL;

        
        /**
*@ManyToOne(targetEntity="vrijveld2dropdownacties")
        */

        
        protected $vrijveld2dropdownacties = NULL;

        
        /**
*@ManyToOne(targetEntity="vrijveld3dropdownacties")
        */

        
        protected $vrijveld3dropdownacties = NULL;

        
        /**
*@ManyToOne(targetEntity="vrijveld4dropdownacties")
        */

        
        protected $vrijveld4dropdownacties = NULL;

        
        /**
*@ManyToOne(targetEntity="department")
        */

        
        protected $department_action = NULL;

        
        /**
*@ManyToOne(targetEntity="employee")
        */

        
        protected $employee_action = NULL;

        
        /**
*@ManyToOne(targetEntity="employee")
        */

        
        protected $employee_verification = NULL;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $newnotification = false;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $changenotification = false;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $expirednotification = false;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $completed_notification = false;

        
        /**
*@OneToMany(targetEntity="timelineentry", mappedBy="action")
        */

        
        protected $timelineentries = NULL;

        
        /**
*@ManyToMany(targetEntity="file")
*@JoinTable(name="action_files_file",joinColumns={@JoinColumn(name="action_files_id", referencedColumnName="id", onDelete="CASCADE")},inverseJoinColumns={@JoinColumn(name="file_id", referencedColumnName="id")})
        */

        
        protected $files = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $_ordering = NULL;

        
        /**
*@ManyToOne(targetEntity="vrijveld1dropdownactiesinspecties")
        */

        
        protected $vrijveld1dropdownactiesinspecties = NULL;

        
        /**
*@ManyToOne(targetEntity="vrijveld2dropdownactiesinspecties")
        */

        
        protected $vrijveld2dropdownactiesinspecties = NULL;

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $part = '';

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $group_ = '';

        
        /**
*@ManyToOne(targetEntity="registratiemaatregel")
        */

        
        protected $registratiemaatregel = NULL;

        


    public function __construct() {
        parent::__construct();
        $this->timelineentries = new \Doctrine\Common\Collections\ArrayCollection();
$this->files = new \Doctrine\Common\Collections\ArrayCollection();
    }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'action';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'number' => array(
                'dbfield' => 'number',
                'type' => 'int',
                'ignore' => false,
            ),
            'action' => array(
                'dbfield' => 'action',
                'type' => 'string',
                'ignore' => false,
            ),
            'remark' => array(
                'dbfield' => 'remark',
                'type' => 'text',
                'ignore' => false,
            ),
            'employee' => array(
                'dbfield' => 'employee',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\employee',
                'alias' => 'employee'
            ),
            'department' => array(
                'dbfield' => 'department',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\department',
                'alias' => 'department'
            ),
            'dateactioncreated' => array(
                'dbfield' => 'dateactioncreated',
                'type' => 'date',
                'ignore' => false,
            ),
            'dateactionplanned' => array(
                'dbfield' => 'dateactionplanned',
                'type' => 'date',
                'ignore' => false,
            ),
            'dateactiondeadline' => array(
                'dbfield' => 'dateactiondeadline',
                'type' => 'date',
                'ignore' => false,
            ),
            'dateactionresolved' => array(
                'dbfield' => 'dateactionresolved',
                'type' => 'date',
                'ignore' => false,
            ),
            'dateactionverified' => array(
                'dbfield' => 'dateactionverified',
                'type' => 'date',
                'ignore' => false,
            ),
            'actiontype' => array(
                'dbfield' => 'actiontype',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\actiontype',
                'alias' => 'actiontype'
            ),
            'actioncategory' => array(
                'dbfield' => 'actioncategory',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\actioncategory',
                'alias' => 'actioncategory'
            ),
            'priority' => array(
                'dbfield' => 'priority',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\priority',
                'alias' => 'priority'
            ),
            'nonconformity' => array(
                'dbfield' => 'nonconformity',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\nonconformity',
                'alias' => 'nonconformity'
            ),
            'memo' => array(
                'dbfield' => 'memo',
                'type' => 'search',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\memo',
                'alias' => 'memo'
            ),
            'vrijveld1' => array(
                'dbfield' => 'vrijveld1',
                'type' => 'text',
                'ignore' => false,
            ),
            'vrijveld2' => array(
                'dbfield' => 'vrijveld2',
                'type' => 'text',
                'ignore' => false,
            ),
            'vrijveld3' => array(
                'dbfield' => 'vrijveld3',
                'type' => 'text',
                'ignore' => false,
            ),
            'vrijveld1dropdownacties_id' => array(
                'dbfield' => 'vrijveld1dropdownacties_id',
                'type' => 'int',
                'ignore' => false,
            ),
            'vrijveld2dropdownacties_id' => array(
                'dbfield' => 'vrijveld2dropdownacties_id',
                'type' => 'int',
                'ignore' => false,
            ),
            'vrijveld1dropdownacties' => array(
                'dbfield' => 'vrijveld1dropdownacties',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\vrijveld1dropdownacties',
                'alias' => 'vrijveld1dropdownacties'
            ),
            'vrijveld2dropdownacties' => array(
                'dbfield' => 'vrijveld2dropdownacties',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\vrijveld2dropdownacties',
                'alias' => 'vrijveld2dropdownacties'
            ),
            'vrijveld3dropdownacties' => array(
                'dbfield' => 'vrijveld3dropdownacties',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\vrijveld3dropdownacties',
                'alias' => 'vrijveld3dropdownacties'
            ),
            'vrijveld4dropdownacties' => array(
                'dbfield' => 'vrijveld4dropdownacties',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\vrijveld4dropdownacties',
                'alias' => 'vrijveld4dropdownacties'
            ),
            'department_action' => array(
                'dbfield' => 'department_action',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\department',
                'alias' => 'department_action'
            ),
            'employee_action' => array(
                'dbfield' => 'employee_action',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\employee',
                'alias' => 'employee_action'
            ),
            'employee_verification' => array(
                'dbfield' => 'employee_verification',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\employee',
                'alias' => 'employee_verification'
            ),
            'newnotification' => array(
                'dbfield' => 'newnotification',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'changenotification' => array(
                'dbfield' => 'changenotification',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'expirednotification' => array(
                'dbfield' => 'expirednotification',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'completed_notification' => array(
                'dbfield' => 'completed_notification',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'timelineentries' => array(
                'dbfield' => 'timelineentries',
                'type' => 'timeline',
                'ignore' => false,
                'relation' => 'OneToMany',
                'entity' => '\nl\actinum\custom\tables\timelineentry',
                'alias' => 'timelineentries'
            ),
            'files' => array(
                'dbfield' => 'files',
                'type' => 'filemultiple',
                'ignore' => false,
                'relation' => 'ManyToMany',
                'entity' => '\nl\actinum\custom\tables\file',
                'alias' => 'files'
            ),
            'vrijveld1dropdownactiesinspecties' => array(
                'dbfield' => 'vrijveld1dropdownactiesinspecties',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\vrijveld1dropdownactiesinspecties',
                'alias' => 'vrijveld1dropdownactiesinspecties'
            ),
            'vrijveld2dropdownactiesinspecties' => array(
                'dbfield' => 'vrijveld2dropdownactiesinspecties',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\vrijveld2dropdownactiesinspecties',
                'alias' => 'vrijveld2dropdownactiesinspecties'
            ),
            'part' => array(
                'dbfield' => 'part',
                'type' => 'text',
                'ignore' => false,
            ),
            'group_' => array(
                'dbfield' => 'group_',
                'type' => 'text',
                'ignore' => false,
            ),
            'registratiemaatregel' => array(
                'dbfield' => 'registratiemaatregel',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\registratiemaatregel',
                'alias' => 'registratiemaatregel'
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "action.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getNumber(){
        return $this->number;
    }

    public function setNumber( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'number' in setter, should be NULL or INT");
        }

        $this->number = $value;
    }

        
    public function getAction(){
        return $this->action;
    }

    public function setAction( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'action' in setter, should be NULL or STRING");
        }

        $this->action = $value;
    }

        
    public function getRemark(){
        return $this->remark;
    }

    public function setRemark( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'remark' in setter, should be NULL or STRING");
        }

        $this->remark = $value;
    }

        
    /** @return \nl\actinum\custom\tables\employee */
    public function getEmployee(){
        return $this->employee;
    }

    public function setEmployee(\nl\actinum\custom\tables\employee $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\employee || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'employee' in setter, should be NULL or instance of \nl\actinum\custom\tables\employee");
        }

        $this->employee = $value;
    }

        
    /** @return \nl\actinum\custom\tables\department */
    public function getDepartment(){
        return $this->department;
    }

    public function setDepartment(\nl\actinum\custom\tables\department $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\department || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'department' in setter, should be NULL or instance of \nl\actinum\custom\tables\department");
        }

        $this->department = $value;
    }

        
    /** @return \DateTime */
    public function getDateactioncreated(){
        return $this->dateactioncreated;
    }

    public function setDateactioncreated(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'dateactioncreated' in setter, should be NULL or instance of \DateTime");
        }

        $this->dateactioncreated = $value;
    }

        
    /** @return \DateTime */
    public function getDateactionplanned(){
        return $this->dateactionplanned;
    }

    public function setDateactionplanned(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'dateactionplanned' in setter, should be NULL or instance of \DateTime");
        }

        $this->dateactionplanned = $value;
    }

        
    /** @return \DateTime */
    public function getDateactiondeadline(){
        return $this->dateactiondeadline;
    }

    public function setDateactiondeadline(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'dateactiondeadline' in setter, should be NULL or instance of \DateTime");
        }

        $this->dateactiondeadline = $value;
    }

        
    /** @return \DateTime */
    public function getDateactionresolved(){
        return $this->dateactionresolved;
    }

    public function setDateactionresolved(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'dateactionresolved' in setter, should be NULL or instance of \DateTime");
        }

        $this->dateactionresolved = $value;
    }

        
    /** @return \DateTime */
    public function getDateactionverified(){
        return $this->dateactionverified;
    }

    public function setDateactionverified(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'dateactionverified' in setter, should be NULL or instance of \DateTime");
        }

        $this->dateactionverified = $value;
    }

        
    /** @return \nl\actinum\custom\tables\actiontype */
    public function getActiontype(){
        return $this->actiontype;
    }

    public function setActiontype(\nl\actinum\custom\tables\actiontype $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\actiontype || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'actiontype' in setter, should be NULL or instance of \nl\actinum\custom\tables\actiontype");
        }

        $this->actiontype = $value;
    }

        
    /** @return \nl\actinum\custom\tables\actioncategory */
    public function getActioncategory(){
        return $this->actioncategory;
    }

    public function setActioncategory(\nl\actinum\custom\tables\actioncategory $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\actioncategory || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'actioncategory' in setter, should be NULL or instance of \nl\actinum\custom\tables\actioncategory");
        }

        $this->actioncategory = $value;
    }

        
    /** @return \nl\actinum\custom\tables\priority */
    public function getPriority(){
        return $this->priority;
    }

    public function setPriority(\nl\actinum\custom\tables\priority $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\priority || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'priority' in setter, should be NULL or instance of \nl\actinum\custom\tables\priority");
        }

        $this->priority = $value;
    }

        
    /** @return \nl\actinum\custom\tables\nonconformity */
    public function getNonconformity(){
        return $this->nonconformity;
    }

    public function setNonconformity(\nl\actinum\custom\tables\nonconformity $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\nonconformity || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'nonconformity' in setter, should be NULL or instance of \nl\actinum\custom\tables\nonconformity");
        }

        $this->nonconformity = $value;
    }

        
    /** @return \nl\actinum\custom\tables\memo */
    public function getMemo(){
        return $this->memo;
    }

    public function setMemo(\nl\actinum\custom\tables\memo $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\memo || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'memo' in setter, should be NULL or instance of \nl\actinum\custom\tables\memo");
        }

        $this->memo = $value;
    }

        
    public function getVrijveld1(){
        return $this->vrijveld1;
    }

    public function setVrijveld1( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld1' in setter, should be NULL or STRING");
        }

        $this->vrijveld1 = $value;
    }

        
    public function getVrijveld2(){
        return $this->vrijveld2;
    }

    public function setVrijveld2( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld2' in setter, should be NULL or STRING");
        }

        $this->vrijveld2 = $value;
    }

        
    public function getVrijveld3(){
        return $this->vrijveld3;
    }

    public function setVrijveld3( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld3' in setter, should be NULL or STRING");
        }

        $this->vrijveld3 = $value;
    }

        
    public function getVrijveld1dropdownacties_id(){
        return $this->vrijveld1dropdownacties_id;
    }

    public function setVrijveld1dropdownacties_id( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld1dropdownacties_id' in setter, should be NULL or INT");
        }

        $this->vrijveld1dropdownacties_id = $value;
    }

        
    public function getVrijveld2dropdownacties_id(){
        return $this->vrijveld2dropdownacties_id;
    }

    public function setVrijveld2dropdownacties_id( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld2dropdownacties_id' in setter, should be NULL or INT");
        }

        $this->vrijveld2dropdownacties_id = $value;
    }

        
    /** @return \nl\actinum\custom\tables\vrijveld1dropdownacties */
    public function getVrijveld1dropdownacties(){
        return $this->vrijveld1dropdownacties;
    }

    public function setVrijveld1dropdownacties(\nl\actinum\custom\tables\vrijveld1dropdownacties $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\vrijveld1dropdownacties || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld1dropdownacties' in setter, should be NULL or instance of \nl\actinum\custom\tables\vrijveld1dropdownacties");
        }

        $this->vrijveld1dropdownacties = $value;
    }

        
    /** @return \nl\actinum\custom\tables\vrijveld2dropdownacties */
    public function getVrijveld2dropdownacties(){
        return $this->vrijveld2dropdownacties;
    }

    public function setVrijveld2dropdownacties(\nl\actinum\custom\tables\vrijveld2dropdownacties $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\vrijveld2dropdownacties || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld2dropdownacties' in setter, should be NULL or instance of \nl\actinum\custom\tables\vrijveld2dropdownacties");
        }

        $this->vrijveld2dropdownacties = $value;
    }

        
    /** @return \nl\actinum\custom\tables\vrijveld3dropdownacties */
    public function getVrijveld3dropdownacties(){
        return $this->vrijveld3dropdownacties;
    }

    public function setVrijveld3dropdownacties(\nl\actinum\custom\tables\vrijveld3dropdownacties $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\vrijveld3dropdownacties || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld3dropdownacties' in setter, should be NULL or instance of \nl\actinum\custom\tables\vrijveld3dropdownacties");
        }

        $this->vrijveld3dropdownacties = $value;
    }

        
    /** @return \nl\actinum\custom\tables\vrijveld4dropdownacties */
    public function getVrijveld4dropdownacties(){
        return $this->vrijveld4dropdownacties;
    }

    public function setVrijveld4dropdownacties(\nl\actinum\custom\tables\vrijveld4dropdownacties $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\vrijveld4dropdownacties || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld4dropdownacties' in setter, should be NULL or instance of \nl\actinum\custom\tables\vrijveld4dropdownacties");
        }

        $this->vrijveld4dropdownacties = $value;
    }

        
    /** @return \nl\actinum\custom\tables\department */
    public function getDepartment_action(){
        return $this->department_action;
    }

    public function setDepartment_action(\nl\actinum\custom\tables\department $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\department || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'department_action' in setter, should be NULL or instance of \nl\actinum\custom\tables\department");
        }

        $this->department_action = $value;
    }

        
    /** @return \nl\actinum\custom\tables\employee */
    public function getEmployee_action(){
        return $this->employee_action;
    }

    public function setEmployee_action(\nl\actinum\custom\tables\employee $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\employee || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'employee_action' in setter, should be NULL or instance of \nl\actinum\custom\tables\employee");
        }

        $this->employee_action = $value;
    }

        
    /** @return \nl\actinum\custom\tables\employee */
    public function getEmployee_verification(){
        return $this->employee_verification;
    }

    public function setEmployee_verification(\nl\actinum\custom\tables\employee $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\employee || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'employee_verification' in setter, should be NULL or instance of \nl\actinum\custom\tables\employee");
        }

        $this->employee_verification = $value;
    }

        
    public function getNewnotification(){
        return $this->newnotification;
    }

    public function setNewnotification( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'newnotification' in setter, should be BOOLEAN");
        }

        $this->newnotification = $value;
    }

        
    public function getChangenotification(){
        return $this->changenotification;
    }

    public function setChangenotification( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'changenotification' in setter, should be BOOLEAN");
        }

        $this->changenotification = $value;
    }

        
    public function getExpirednotification(){
        return $this->expirednotification;
    }

    public function setExpirednotification( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'expirednotification' in setter, should be BOOLEAN");
        }

        $this->expirednotification = $value;
    }

        
    public function getCompleted_notification(){
        return $this->completed_notification;
    }

    public function setCompleted_notification( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'completed_notification' in setter, should be BOOLEAN");
        }

        $this->completed_notification = $value;
    }

        
    public function getTimelineentries(){
        return $this->timelineentries;
    }

    public function setTimelineentries( $value){
                $this->timelineentries = $value;
    }

        
    public function getFiles(){
        return $this->files;
    }

    public function setFiles( $value){
                $this->files = $value;
    }

        
    public function get_ordering(){
        return $this->_ordering;
    }

    public function set_ordering( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_ordering' in setter, should be NULL or INT");
        }

        $this->_ordering = $value;
    }

        
    /** @return \nl\actinum\custom\tables\vrijveld1dropdownactiesinspecties */
    public function getVrijveld1dropdownactiesinspecties(){
        return $this->vrijveld1dropdownactiesinspecties;
    }

    public function setVrijveld1dropdownactiesinspecties(\nl\actinum\custom\tables\vrijveld1dropdownactiesinspecties $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\vrijveld1dropdownactiesinspecties || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld1dropdownactiesinspecties' in setter, should be NULL or instance of \nl\actinum\custom\tables\vrijveld1dropdownactiesinspecties");
        }

        $this->vrijveld1dropdownactiesinspecties = $value;
    }

        
    /** @return \nl\actinum\custom\tables\vrijveld2dropdownactiesinspecties */
    public function getVrijveld2dropdownactiesinspecties(){
        return $this->vrijveld2dropdownactiesinspecties;
    }

    public function setVrijveld2dropdownactiesinspecties(\nl\actinum\custom\tables\vrijveld2dropdownactiesinspecties $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\vrijveld2dropdownactiesinspecties || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld2dropdownactiesinspecties' in setter, should be NULL or instance of \nl\actinum\custom\tables\vrijveld2dropdownactiesinspecties");
        }

        $this->vrijveld2dropdownactiesinspecties = $value;
    }

        
    public function getPart(){
        return $this->part;
    }

    public function setPart( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'part' in setter, should be NULL or STRING");
        }

        $this->part = $value;
    }

        
    public function getGroup_(){
        return $this->group_;
    }

    public function setGroup_( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'group_' in setter, should be NULL or STRING");
        }

        $this->group_ = $value;
    }

        
    /** @return \nl\actinum\custom\tables\registratiemaatregel */
    public function getRegistratiemaatregel(){
        return $this->registratiemaatregel;
    }

    public function setRegistratiemaatregel(\nl\actinum\custom\tables\registratiemaatregel $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\registratiemaatregel || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'registratiemaatregel' in setter, should be NULL or instance of \nl\actinum\custom\tables\registratiemaatregel");
        }

        $this->registratiemaatregel = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
  0 => 'files',
);

}
}
?>