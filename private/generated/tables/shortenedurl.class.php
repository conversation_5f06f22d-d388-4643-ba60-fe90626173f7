<?php
namespace nl\actinum\generated\tables{

class shortenedurl extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = false;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
);


        /**
*@Column(type="string", nullable=false, unique=true)
        */

        
        protected $hash = '';

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $fullurl = '';

        


    public function __construct() {
        parent::__construct();
            }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'shortenedurl';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'hash' => array(
                'dbfield' => 'hash',
                'type' => 'string',
                'ignore' => false,
            ),
            'fullurl' => array(
                'dbfield' => 'fullurl',
                'type' => 'text',
                'ignore' => false,
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "shortenedurl.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getHash(){
        return $this->hash;
    }

    public function setHash( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'hash' in setter, should be NULL or STRING");
        }

        $this->hash = $value;
    }

        
    public function getFullurl(){
        return $this->fullurl;
    }

    public function setFullurl( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'fullurl' in setter, should be NULL or STRING");
        }

        $this->fullurl = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
);

}
}
?>