<?php
namespace nl\actinum\generated\tables{

class leveranciervragenlijst extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = false;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
  0 => 'n20515n',
);


        /**
*@ManyToOne(targetEntity="supplierquestionlisttype")
        */

        
        protected $supplierquestionlisttype = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $name = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $code = '';

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $active = false;

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $description = '';

        
        /**
*@ManyToOne(targetEntity="department")
        */

        
        protected $department = NULL;

        
        /**
*@ManyToOne(targetEntity="employee")
        */

        
        protected $employee = NULL;

        
        /**
*@OneToMany(targetEntity="leveranciervraag", mappedBy="leveranciervragenlijst", fetch="EXTRA_LAZY")
*@OrderBy({"_ordering" = "ASC"})
        */

        
        protected $leveranciervragen = NULL;

        
        /**
*@OneToMany(targetEntity="leveranciervraagantwoordoptie", mappedBy="leveranciervragenlijst", fetch="EXTRA_LAZY")
*@OrderBy({"_ordering" = "ASC"})
        */

        
        protected $leveranciervraagantwoordopties = NULL;

        
        /**
*@ManyToOne(targetEntity="supplierquestionnairepart")
        */

        
        protected $supplierquestionnairepartdummy = NULL;

        
        /**
*@ManyToOne(targetEntity="supplierreviewpart")
        */

        
        protected $supplierreviewpartdummy = NULL;

        


    public function __construct() {
        parent::__construct();
        $this->leveranciervragen = new \Doctrine\Common\Collections\ArrayCollection();
$this->leveranciervraagantwoordopties = new \Doctrine\Common\Collections\ArrayCollection();
    }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'leveranciervragenlijst';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'supplierquestionlisttype' => array(
                'dbfield' => 'supplierquestionlisttype',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\supplierquestionlisttype',
                'alias' => 'supplierquestionlisttype'
            ),
            'name' => array(
                'dbfield' => 'name',
                'type' => 'string',
                'ignore' => false,
            ),
            'code' => array(
                'dbfield' => 'code',
                'type' => 'string',
                'ignore' => false,
            ),
            'active' => array(
                'dbfield' => 'active',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'description' => array(
                'dbfield' => 'description',
                'type' => 'text',
                'ignore' => false,
            ),
            'department' => array(
                'dbfield' => 'department',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\department',
                'alias' => 'department'
            ),
            'employee' => array(
                'dbfield' => 'employee',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\employee',
                'alias' => 'employee'
            ),
            'leveranciervragen' => array(
                'dbfield' => 'leveranciervragen',
                'type' => 'plusbuttongridcollapsible',
                'ignore' => false,
                'relation' => 'OneToMany',
                'entity' => '\nl\actinum\custom\tables\UNKNOWN',
                'alias' => 'leveranciervragen'
            ),
            'leveranciervraagantwoordopties' => array(
                'dbfield' => 'leveranciervraagantwoordopties',
                'type' => 'plusbuttongrid',
                'ignore' => false,
                'relation' => 'OneToMany',
                'entity' => '\nl\actinum\custom\tables\leveranciervraagantwoordoptie',
                'alias' => 'leveranciervraagantwoordopties'
            ),
            'supplierquestionnairepartdummy' => array(
                'dbfield' => 'supplierquestionnairepartdummy',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\supplierquestionnairepart',
                'alias' => 'supplierquestionnairepartdummy'
            ),
            'supplierreviewpartdummy' => array(
                'dbfield' => 'supplierreviewpartdummy',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\supplierreviewpart',
                'alias' => 'supplierreviewpartdummy'
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "leveranciervragenlijst.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    /** @return \nl\actinum\custom\tables\supplierquestionlisttype */
    public function getSupplierquestionlisttype(){
        return $this->supplierquestionlisttype;
    }

    public function setSupplierquestionlisttype(\nl\actinum\custom\tables\supplierquestionlisttype $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\supplierquestionlisttype || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'supplierquestionlisttype' in setter, should be NULL or instance of \nl\actinum\custom\tables\supplierquestionlisttype");
        }

        $this->supplierquestionlisttype = $value;
    }

        
    public function getName(){
        return $this->name;
    }

    public function setName( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'name' in setter, should be NULL or STRING");
        }

        $this->name = $value;
    }

        
    public function getCode(){
        return $this->code;
    }

    public function setCode( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'code' in setter, should be NULL or STRING");
        }

        $this->code = $value;
    }

        
    public function getActive(){
        return $this->active;
    }

    public function setActive( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'active' in setter, should be BOOLEAN");
        }

        $this->active = $value;
    }

        
    public function getDescription(){
        return $this->description;
    }

    public function setDescription( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'description' in setter, should be NULL or STRING");
        }

        $this->description = $value;
    }

        
    /** @return \nl\actinum\custom\tables\department */
    public function getDepartment(){
        return $this->department;
    }

    public function setDepartment(\nl\actinum\custom\tables\department $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\department || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'department' in setter, should be NULL or instance of \nl\actinum\custom\tables\department");
        }

        $this->department = $value;
    }

        
    /** @return \nl\actinum\custom\tables\employee */
    public function getEmployee(){
        return $this->employee;
    }

    public function setEmployee(\nl\actinum\custom\tables\employee $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\employee || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'employee' in setter, should be NULL or instance of \nl\actinum\custom\tables\employee");
        }

        $this->employee = $value;
    }

        
    public function getLeveranciervragen(){
        return $this->leveranciervragen;
    }

    public function setLeveranciervragen( $value){
                $this->leveranciervragen = $value;
    }

        
    public function getLeveranciervraagantwoordopties(){
        return $this->leveranciervraagantwoordopties;
    }

    public function setLeveranciervraagantwoordopties( $value){
                $this->leveranciervraagantwoordopties = $value;
    }

        
    /** @return \nl\actinum\custom\tables\supplierquestionnairepart */
    public function getSupplierquestionnairepartdummy(){
        return $this->supplierquestionnairepartdummy;
    }

    public function setSupplierquestionnairepartdummy(\nl\actinum\custom\tables\supplierquestionnairepart $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\supplierquestionnairepart || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'supplierquestionnairepartdummy' in setter, should be NULL or instance of \nl\actinum\custom\tables\supplierquestionnairepart");
        }

        $this->supplierquestionnairepartdummy = $value;
    }

        
    /** @return \nl\actinum\custom\tables\supplierreviewpart */
    public function getSupplierreviewpartdummy(){
        return $this->supplierreviewpartdummy;
    }

    public function setSupplierreviewpartdummy(\nl\actinum\custom\tables\supplierreviewpart $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\supplierreviewpart || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'supplierreviewpartdummy' in setter, should be NULL or instance of \nl\actinum\custom\tables\supplierreviewpart");
        }

        $this->supplierreviewpartdummy = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
);

}
}
?>