<?php
namespace nl\actinum\generated\tables{

class catch_zone extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = true;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
);


        /**
*@ManyToOne(targetEntity="systemsettings", inversedBy="ps_catch_zone")
*@JoinColumn(name="systemsettings_id", referencedColumnName="id", onDelete="CASCADE")
        */

        
        protected $systemsettings = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $_ordering = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $name = '';

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $psid = NULL;

        


    public function __construct() {
        parent::__construct();
            }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'catch_zone';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'name' => array(
                'dbfield' => 'name',
                'type' => 'string',
                'ignore' => false,
            ),
            'psid' => array(
                'dbfield' => 'psid',
                'type' => 'int',
                'ignore' => false,
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "catch_zone.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getSystemsettings(){
        return $this->systemsettings;
    }

    public function setSystemsettings( $value){
                $this->systemsettings = $value;
    }

        
    public function get_ordering(){
        return $this->_ordering;
    }

    public function set_ordering( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_ordering' in setter, should be NULL or INT");
        }

        $this->_ordering = $value;
    }

        
    public function getName(){
        return $this->name;
    }

    public function setName( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'name' in setter, should be NULL or STRING");
        }

        $this->name = $value;
    }

        
    public function getPsid(){
        return $this->psid;
    }

    public function setPsid( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'psid' in setter, should be NULL or INT");
        }

        $this->psid = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
);

}
}
?>