<?php
namespace nl\actinum\generated\tables{

class subproduct extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = true;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
);


        /**
*@ManyToOne(targetEntity="product", inversedBy="subproducts")
*@JoinColumn(name="product_id", referencedColumnName="id", onDelete="CASCADE")
        */

        
        protected $product = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $_ordering = NULL;

        
        /**
*@ManyToOne(targetEntity="product")
        */

        
        protected $product2 = NULL;

        
        /**
*@Column(type="float", nullable=true)
        */

        
        protected $mixpercentage = NULL;

        


    public function __construct() {
        parent::__construct();
            }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'subproduct';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'product' => array(
                'dbfield' => 'product',
                'type' => 'parentreference',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\product',
                'alias' => 'product'
            ),
            'product2' => array(
                'dbfield' => 'product2',
                'type' => 'search',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\product',
                'alias' => 'product2'
            ),
            'mixpercentage' => array(
                'dbfield' => 'mixpercentage',
                'type' => 'percentage',
                'ignore' => false,
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "subproduct.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getProduct(){
        return $this->product;
    }

    public function setProduct( $value){
                $this->product = $value;
    }

        
    public function get_ordering(){
        return $this->_ordering;
    }

    public function set_ordering( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_ordering' in setter, should be NULL or INT");
        }

        $this->_ordering = $value;
    }

        
    public function getProduct2(){
        return $this->product2;
    }

    public function setProduct2(\nl\actinum\custom\tables\product $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\product || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'product2' in setter, should be NULL or instance of \nl\actinum\custom\tables\product");
        }

        $this->product2 = $value;
    }

        
    public function getMixpercentage(){
        return $this->mixpercentage;
    }

    public function setMixpercentage( $value){
        
        if (!(is_float($value) || is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'mixpercentage' in setter, should be NULL, INT or FLOAT");
        }

        $this->mixpercentage = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
);

}
}
?>