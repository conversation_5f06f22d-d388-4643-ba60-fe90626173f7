<?php
namespace nl\actinum\generated\tables{

class audit extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
  0 => 'n4147n',
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = true;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
  0 => 'n3898n',
);


        /**
*@ManyToOne(targetEntity="auditstatus")
        */

        
        protected $status = NULL;

        
        /**
*@ManyToOne(targetEntity="questionnaire")
        */

        
        protected $questionnaire = NULL;

        
        /**
*@Column(type="date", nullable=true)
        */

        
        protected $date = NULL;

        
        /**
*@Column(type="datetimetz", nullable=true)
        */

        
        protected $dateexecuted = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $number = '';

        
        /**
*@ManyToOne(targetEntity="department")
        */

        
        protected $department = NULL;

        
        /**
*@ManyToOne(targetEntity="employee")
        */

        
        protected $employee = NULL;

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $conclusion = '';

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $report_send = false;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $vrijveld1 = false;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $vrijveld2 = false;

        
        /**
*@ManyToOne(targetEntity="vrijdropdown1inspectie")
        */

        
        protected $vrijdropdown1inspectie = NULL;

        
        /**
*@ManyToMany(targetEntity="systemsettings", mappedBy="conclusion_filter")
        */

        
        protected $conclusion_filter = NULL;

        
        /**
*@ManyToOne(targetEntity="audit")
        */

        
        protected $_parent = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $_ordering = NULL;

        
        /**
*@ManyToOne(targetEntity="department")
        */

        
        protected $advisor_department = NULL;

        
        /**
*@ManyToOne(targetEntity="employee")
        */

        
        protected $advisor_employee = NULL;

        
        /**
*@Column(type="time", nullable=true)
        */

        
        protected $timestart = NULL;

        
        /**
*@Column(type="time", nullable=true)
        */

        
        protected $timestop = NULL;

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $recommendation = '';

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $ignoreinstatistics = false;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $companycertificatesend = false;

        
        /**
*@ManyToOne(targetEntity="workinggroup")
        */

        
        protected $workinggroup = NULL;

        
        /**
*@OneToMany(targetEntity="auditquestionanswer", mappedBy="audit")
        */

        
        protected $answers = NULL;

        
        /**
*@OneToMany(targetEntity="nonconformity", mappedBy="audit", fetch="EXTRA_LAZY")
*@OrderBy({"_ordering" = "ASC"})
        */

        
        protected $nonconformities = NULL;

        
        /**
*@OneToMany(targetEntity="questionremark", mappedBy="audit", fetch="EXTRA_LAZY")
*@OrderBy({"_ordering" = "ASC"})
        */

        
        protected $questionremark = NULL;

        
        /**
*@OneToMany(targetEntity="timelineentry", mappedBy="audit")
        */

        
        protected $timelineentries = NULL;

        
        /**
*@ManyToMany(targetEntity="file")
*@JoinTable(name="audit_files_file",joinColumns={@JoinColumn(name="audit_files_id", referencedColumnName="id", onDelete="CASCADE")},inverseJoinColumns={@JoinColumn(name="file_id", referencedColumnName="id")})
        */

        
        protected $files = NULL;

        
        /**
*@ManyToOne(targetEntity="answertype")
        */

        
        protected $answertype = NULL;

        
        /**
*@ManyToOne(targetEntity="questionansweroption")
        */

        
        protected $questionansweroption = NULL;

        
        /**
*@ManyToMany(targetEntity="file")
*@JoinTable(name="audit_photos_file",joinColumns={@JoinColumn(name="audit_photos_id", referencedColumnName="id", onDelete="CASCADE")},inverseJoinColumns={@JoinColumn(name="file_id", referencedColumnName="id")})
        */

        
        protected $photos = NULL;

        
        /**
*@ManyToOne(targetEntity="audit")
        */

        
        protected $parentaudit = NULL;

        


    public function __construct() {
        parent::__construct();
        $this->answers = new \Doctrine\Common\Collections\ArrayCollection();
$this->nonconformities = new \Doctrine\Common\Collections\ArrayCollection();
$this->questionremark = new \Doctrine\Common\Collections\ArrayCollection();
$this->timelineentries = new \Doctrine\Common\Collections\ArrayCollection();
$this->conclusion_filter = new \Doctrine\Common\Collections\ArrayCollection();
$this->files = new \Doctrine\Common\Collections\ArrayCollection();
$this->photos = new \Doctrine\Common\Collections\ArrayCollection();
    }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'audit';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'status' => array(
                'dbfield' => 'status',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\auditstatus',
                'alias' => 'status'
            ),
            'questionnaire' => array(
                'dbfield' => 'questionnaire',
                'type' => 'search',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\questionnaire',
                'alias' => 'questionnaire'
            ),
            'date' => array(
                'dbfield' => 'date',
                'type' => 'date',
                'ignore' => false,
            ),
            'dateexecuted' => array(
                'dbfield' => 'dateexecuted',
                'type' => 'datetime',
                'ignore' => false,
            ),
            'number' => array(
                'dbfield' => 'number',
                'type' => 'string',
                'ignore' => false,
            ),
            'department' => array(
                'dbfield' => 'department',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\department',
                'alias' => 'department'
            ),
            'employee' => array(
                'dbfield' => 'employee',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\employee',
                'alias' => 'employee'
            ),
            'conclusion' => array(
                'dbfield' => 'conclusion',
                'type' => 'text',
                'ignore' => false,
            ),
            'report_send' => array(
                'dbfield' => 'report_send',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'vrijveld1' => array(
                'dbfield' => 'vrijveld1',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'vrijveld2' => array(
                'dbfield' => 'vrijveld2',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'vrijdropdown1inspectie' => array(
                'dbfield' => 'vrijdropdown1inspectie',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\vrijdropdown1inspectie',
                'alias' => 'vrijdropdown1inspectie'
            ),
            'conclusion_filter' => array(
                'dbfield' => 'conclusion_filter',
                'type' => 'linkform',
                'ignore' => false,
                'relation' => 'ManyToMany',
                'entity' => '\nl\actinum\custom\tables\systemsettings',
                'alias' => 'conclusion_filter'
            ),
            '_parent' => array(
                'dbfield' => '_parent',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\audit',
                'alias' => '_parent'
            ),
            'advisor_department' => array(
                'dbfield' => 'advisor_department',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\department',
                'alias' => 'advisor_department'
            ),
            'advisor_employee' => array(
                'dbfield' => 'advisor_employee',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\employee',
                'alias' => 'advisor_employee'
            ),
            'timestart' => array(
                'dbfield' => 'timestart',
                'type' => 'time',
                'ignore' => false,
            ),
            'timestop' => array(
                'dbfield' => 'timestop',
                'type' => 'time',
                'ignore' => false,
            ),
            'recommendation' => array(
                'dbfield' => 'recommendation',
                'type' => 'text',
                'ignore' => false,
            ),
            'ignoreinstatistics' => array(
                'dbfield' => 'ignoreinstatistics',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'companycertificatesend' => array(
                'dbfield' => 'companycertificatesend',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'workinggroup' => array(
                'dbfield' => 'workinggroup',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\workinggroup',
                'alias' => 'workinggroup'
            ),
            'answers' => array(
                'dbfield' => 'answers',
                'type' => 'gridcollapsible',
                'ignore' => false,
                'relation' => 'OneToMany',
                'entity' => '\nl\actinum\custom\tables\UNKNOWN',
                'alias' => 'answers'
            ),
            'nonconformities' => array(
                'dbfield' => 'nonconformities',
                'type' => 'plusbuttongrid',
                'ignore' => false,
                'relation' => 'OneToMany',
                'entity' => '\nl\actinum\custom\tables\nonconformity',
                'alias' => 'nonconformities'
            ),
            'questionremark' => array(
                'dbfield' => 'questionremark',
                'type' => 'plusbuttongrid',
                'ignore' => false,
                'relation' => 'OneToMany',
                'entity' => '\nl\actinum\custom\tables\questionremark',
                'alias' => 'questionremark'
            ),
            'timelineentries' => array(
                'dbfield' => 'timelineentries',
                'type' => 'timeline',
                'ignore' => false,
                'relation' => 'OneToMany',
                'entity' => '\nl\actinum\custom\tables\timelineentry',
                'alias' => 'timelineentries'
            ),
            'files' => array(
                'dbfield' => 'files',
                'type' => 'filemultiple',
                'ignore' => false,
                'relation' => 'ManyToMany',
                'entity' => '\nl\actinum\custom\tables\file',
                'alias' => 'files'
            ),
            'answertype' => array(
                'dbfield' => 'answertype',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\answertype',
                'alias' => 'answertype'
            ),
            'questionansweroption' => array(
                'dbfield' => 'questionansweroption',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\questionansweroption',
                'alias' => 'questionansweroption'
            ),
            'photos' => array(
                'dbfield' => 'photos',
                'type' => 'filemultiple',
                'ignore' => false,
                'relation' => 'ManyToMany',
                'entity' => '\nl\actinum\custom\tables\file',
                'alias' => 'photos'
            ),
            'parentaudit' => array(
                'dbfield' => 'parentaudit',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\audit',
                'alias' => 'parentaudit'
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "audit.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    /** @return \nl\actinum\custom\tables\auditstatus */
    public function getStatus(){
        return $this->status;
    }

    public function setStatus(\nl\actinum\custom\tables\auditstatus $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\auditstatus || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'status' in setter, should be NULL or instance of \nl\actinum\custom\tables\auditstatus");
        }

        $this->status = $value;
    }

        
    /** @return \nl\actinum\custom\tables\questionnaire */
    public function getQuestionnaire(){
        return $this->questionnaire;
    }

    public function setQuestionnaire(\nl\actinum\custom\tables\questionnaire $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\questionnaire || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'questionnaire' in setter, should be NULL or instance of \nl\actinum\custom\tables\questionnaire");
        }

        $this->questionnaire = $value;
    }

        
    /** @return \DateTime */
    public function getDate(){
        return $this->date;
    }

    public function setDate(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'date' in setter, should be NULL or instance of \DateTime");
        }

        $this->date = $value;
    }

        
    /** @return \DateTime */
    public function getDateexecuted(){
        return $this->dateexecuted;
    }

    public function setDateexecuted(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'dateexecuted' in setter, should be NULL or instance of \DateTime");
        }

        $this->dateexecuted = $value;
    }

        
    public function getNumber(){
        return $this->number;
    }

    public function setNumber( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'number' in setter, should be NULL or STRING");
        }

        $this->number = $value;
    }

        
    /** @return \nl\actinum\custom\tables\department */
    public function getDepartment(){
        return $this->department;
    }

    public function setDepartment(\nl\actinum\custom\tables\department $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\department || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'department' in setter, should be NULL or instance of \nl\actinum\custom\tables\department");
        }

        $this->department = $value;
    }

        
    /** @return \nl\actinum\custom\tables\employee */
    public function getEmployee(){
        return $this->employee;
    }

    public function setEmployee(\nl\actinum\custom\tables\employee $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\employee || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'employee' in setter, should be NULL or instance of \nl\actinum\custom\tables\employee");
        }

        $this->employee = $value;
    }

        
    public function getConclusion(){
        return $this->conclusion;
    }

    public function setConclusion( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'conclusion' in setter, should be NULL or STRING");
        }

        $this->conclusion = $value;
    }

        
    public function getReport_send(){
        return $this->report_send;
    }

    public function setReport_send( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'report_send' in setter, should be BOOLEAN");
        }

        $this->report_send = $value;
    }

        
    public function getVrijveld1(){
        return $this->vrijveld1;
    }

    public function setVrijveld1( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld1' in setter, should be BOOLEAN");
        }

        $this->vrijveld1 = $value;
    }

        
    public function getVrijveld2(){
        return $this->vrijveld2;
    }

    public function setVrijveld2( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'vrijveld2' in setter, should be BOOLEAN");
        }

        $this->vrijveld2 = $value;
    }

        
    /** @return \nl\actinum\custom\tables\vrijdropdown1inspectie */
    public function getVrijdropdown1inspectie(){
        return $this->vrijdropdown1inspectie;
    }

    public function setVrijdropdown1inspectie(\nl\actinum\custom\tables\vrijdropdown1inspectie $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\vrijdropdown1inspectie || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'vrijdropdown1inspectie' in setter, should be NULL or instance of \nl\actinum\custom\tables\vrijdropdown1inspectie");
        }

        $this->vrijdropdown1inspectie = $value;
    }

        
    public function getConclusion_filter(){
        return $this->conclusion_filter;
    }

    public function setConclusion_filter( $value){
                $this->conclusion_filter = $value;
    }

        
    /** @return \nl\actinum\custom\tables\audit */
    public function get_parent(){
        return $this->_parent;
    }

    public function set_parent(\nl\actinum\custom\tables\audit $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\audit || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_parent' in setter, should be NULL or instance of \nl\actinum\custom\tables\audit");
        }

        $this->_parent = $value;
    }

        
    public function get_ordering(){
        return $this->_ordering;
    }

    public function set_ordering( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_ordering' in setter, should be NULL or INT");
        }

        $this->_ordering = $value;
    }

        
    /** @return \nl\actinum\custom\tables\department */
    public function getAdvisor_department(){
        return $this->advisor_department;
    }

    public function setAdvisor_department(\nl\actinum\custom\tables\department $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\department || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'advisor_department' in setter, should be NULL or instance of \nl\actinum\custom\tables\department");
        }

        $this->advisor_department = $value;
    }

        
    /** @return \nl\actinum\custom\tables\employee */
    public function getAdvisor_employee(){
        return $this->advisor_employee;
    }

    public function setAdvisor_employee(\nl\actinum\custom\tables\employee $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\employee || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'advisor_employee' in setter, should be NULL or instance of \nl\actinum\custom\tables\employee");
        }

        $this->advisor_employee = $value;
    }

        
    /** @return \DateTime */
    public function getTimestart(){
        return $this->timestart;
    }

    public function setTimestart(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'timestart' in setter, should be NULL or instance of \DateTime");
        }

        $this->timestart = $value;
    }

        
    /** @return \DateTime */
    public function getTimestop(){
        return $this->timestop;
    }

    public function setTimestop(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'timestop' in setter, should be NULL or instance of \DateTime");
        }

        $this->timestop = $value;
    }

        
    public function getRecommendation(){
        return $this->recommendation;
    }

    public function setRecommendation( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'recommendation' in setter, should be NULL or STRING");
        }

        $this->recommendation = $value;
    }

        
    public function getIgnoreinstatistics(){
        return $this->ignoreinstatistics;
    }

    public function setIgnoreinstatistics( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'ignoreinstatistics' in setter, should be BOOLEAN");
        }

        $this->ignoreinstatistics = $value;
    }

        
    public function getCompanycertificatesend(){
        return $this->companycertificatesend;
    }

    public function setCompanycertificatesend( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'companycertificatesend' in setter, should be BOOLEAN");
        }

        $this->companycertificatesend = $value;
    }

        
    /** @return \nl\actinum\custom\tables\workinggroup */
    public function getWorkinggroup(){
        return $this->workinggroup;
    }

    public function setWorkinggroup(\nl\actinum\custom\tables\workinggroup $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\workinggroup || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'workinggroup' in setter, should be NULL or instance of \nl\actinum\custom\tables\workinggroup");
        }

        $this->workinggroup = $value;
    }

        
    public function getAnswers(){
        return $this->answers;
    }

    public function setAnswers( $value){
                $this->answers = $value;
    }

        
    public function getNonconformities(){
        return $this->nonconformities;
    }

    public function setNonconformities( $value){
                $this->nonconformities = $value;
    }

        
    public function getQuestionremark(){
        return $this->questionremark;
    }

    public function setQuestionremark( $value){
                $this->questionremark = $value;
    }

        
    public function getTimelineentries(){
        return $this->timelineentries;
    }

    public function setTimelineentries( $value){
                $this->timelineentries = $value;
    }

        
    public function getFiles(){
        return $this->files;
    }

    public function setFiles( $value){
                $this->files = $value;
    }

        
    /** @return \nl\actinum\custom\tables\answertype */
    public function getAnswertype(){
        return $this->answertype;
    }

    public function setAnswertype(\nl\actinum\custom\tables\answertype $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\answertype || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'answertype' in setter, should be NULL or instance of \nl\actinum\custom\tables\answertype");
        }

        $this->answertype = $value;
    }

        
    /** @return \nl\actinum\custom\tables\questionansweroption */
    public function getQuestionansweroption(){
        return $this->questionansweroption;
    }

    public function setQuestionansweroption(\nl\actinum\custom\tables\questionansweroption $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\questionansweroption || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'questionansweroption' in setter, should be NULL or instance of \nl\actinum\custom\tables\questionansweroption");
        }

        $this->questionansweroption = $value;
    }

        
    public function getPhotos(){
        return $this->photos;
    }

    public function setPhotos( $value){
                $this->photos = $value;
    }

        
    /** @return \nl\actinum\custom\tables\audit */
    public function getParentaudit(){
        return $this->parentaudit;
    }

    public function setParentaudit(\nl\actinum\custom\tables\audit $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\audit || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'parentaudit' in setter, should be NULL or instance of \nl\actinum\custom\tables\audit");
        }

        $this->parentaudit = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
  0 => 'files',
  1 => 'photos',
);

}
}
?>