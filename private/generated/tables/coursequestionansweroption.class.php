<?php
namespace nl\actinum\generated\tables{

class coursequestionansweroption extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = true;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
);


        /**
*@ManyToOne(targetEntity="course", inversedBy="coursequestionansweroptions")
*@JoinColumn(name="course_id", referencedColumnName="id", onDelete="CASCADE")
        */

        
        protected $course = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $_ordering = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $answer = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $answermatch = '';

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $correct = false;

        
        /**
*@ManyToOne(targetEntity="slide", inversedBy="coursequestionansweroptions")
*@JoinColumn(name="slide_id", referencedColumnName="id", onDelete="CASCADE")
        */

        
        protected $slide = NULL;

        


    public function __construct() {
        parent::__construct();
            }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'coursequestionansweroption';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'course' => array(
                'dbfield' => 'course',
                'type' => 'parentreference',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\course',
                'alias' => 'course'
            ),
            'answer' => array(
                'dbfield' => 'answer',
                'type' => 'string',
                'ignore' => false,
            ),
            'answermatch' => array(
                'dbfield' => 'answermatch',
                'type' => 'string',
                'ignore' => false,
            ),
            'correct' => array(
                'dbfield' => 'correct',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'slide' => array(
                'dbfield' => 'slide',
                'type' => 'parentreference',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\slide',
                'alias' => 'slide'
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "coursequestionansweroption.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getCourse(){
        return $this->course;
    }

    public function setCourse( $value){
                $this->course = $value;
    }

        
    public function get_ordering(){
        return $this->_ordering;
    }

    public function set_ordering( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_ordering' in setter, should be NULL or INT");
        }

        $this->_ordering = $value;
    }

        
    public function getAnswer(){
        return $this->answer;
    }

    public function setAnswer( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'answer' in setter, should be NULL or STRING");
        }

        $this->answer = $value;
    }

        
    public function getAnswermatch(){
        return $this->answermatch;
    }

    public function setAnswermatch( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'answermatch' in setter, should be NULL or STRING");
        }

        $this->answermatch = $value;
    }

        
    public function getCorrect(){
        return $this->correct;
    }

    public function setCorrect( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'correct' in setter, should be BOOLEAN");
        }

        $this->correct = $value;
    }

        
    public function getSlide(){
        return $this->slide;
    }

    public function setSlide( $value){
                $this->slide = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
);

}
}
?>