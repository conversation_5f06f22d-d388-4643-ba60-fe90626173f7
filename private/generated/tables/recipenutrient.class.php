<?php
namespace nl\actinum\generated\tables{

class recipenutrient extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = true;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
  0 => 'n29299n',
);


        /**
*@ManyToOne(targetEntity="recipe", inversedBy="recipenutrients")
*@JoinColumn(name="recipe_id", referencedColumnName="id", onDelete="CASCADE")
        */

        
        protected $recipe = NULL;

        
        /**
*@ManyToOne(targetEntity="nutrient")
        */

        
        protected $nutrient = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $remark = '';

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $_ordering = NULL;

        


    public function __construct() {
        parent::__construct();
            }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'recipenutrient';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'recipe' => array(
                'dbfield' => 'recipe',
                'type' => 'search',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\recipe',
                'alias' => 'recipe'
            ),
            'nutrient' => array(
                'dbfield' => 'nutrient',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\nutrient',
                'alias' => 'nutrient'
            ),
            'remark' => array(
                'dbfield' => 'remark',
                'type' => 'string',
                'ignore' => false,
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "recipenutrient.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    /** @return \nl\actinum\custom\tables\recipe */
    public function getRecipe(){
        return $this->recipe;
    }

    public function setRecipe(\nl\actinum\custom\tables\recipe $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\recipe || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'recipe' in setter, should be NULL or instance of \nl\actinum\custom\tables\recipe");
        }

        $this->recipe = $value;
    }

        
    /** @return \nl\actinum\custom\tables\nutrient */
    public function getNutrient(){
        return $this->nutrient;
    }

    public function setNutrient(\nl\actinum\custom\tables\nutrient $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\nutrient || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'nutrient' in setter, should be NULL or instance of \nl\actinum\custom\tables\nutrient");
        }

        $this->nutrient = $value;
    }

        
    public function getRemark(){
        return $this->remark;
    }

    public function setRemark( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'remark' in setter, should be NULL or STRING");
        }

        $this->remark = $value;
    }

        
    public function get_ordering(){
        return $this->_ordering;
    }

    public function set_ordering( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_ordering' in setter, should be NULL or INT");
        }

        $this->_ordering = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
);

}
}
?>