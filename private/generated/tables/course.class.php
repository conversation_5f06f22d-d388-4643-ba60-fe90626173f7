<?php
namespace nl\actinum\generated\tables{

class course extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
  0 => 'n14288n',
  1 => 'n6578n',
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = false;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
  0 => 'n6647n',
  1 => 'n8825n',
);


        /**
*@Column(type="string", nullable=false)
        */

        
        protected $zoeknaam = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $name = '';

        
        /**
*@ManyToOne(targetEntity="language")
        */

        
        protected $language = NULL;

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $remarks = '';

        
        /**
*@ManyToOne(targetEntity="coursetype")
        */

        
        protected $coursetype = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $number = NULL;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $active = false;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $maxtimeminutes = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $maxattempts = NULL;

        
        /**
*@Column(type="float", nullable=true)
        */

        
        protected $successpercentage = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $validmonths = NULL;

        
        /**
*@ManyToOne(targetEntity="department")
        */

        
        protected $department = NULL;

        
        /**
*@ManyToOne(targetEntity="employee")
        */

        
        protected $employee = NULL;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $isreadonly = false;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $minimaletijdsduur = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $coursesectionid = NULL;

        
        /**
*@ManyToOne(targetEntity="coursequestiontype")
        */

        
        protected $coursequestiontype = NULL;

        
        /**
*@OneToMany(targetEntity="coursequestionansweroption", mappedBy="course", fetch="EXTRA_LAZY")
*@OrderBy({"_ordering" = "ASC"})
        */

        
        protected $coursequestionansweroptions = NULL;

        
        /**
*@ManyToOne(targetEntity="coursesectiontype")
        */

        
        protected $coursesectiontype = NULL;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $userpersisted = false;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $settings_showinactive = false;

        
        /**
*@OneToMany(targetEntity="coursesection", mappedBy="course", fetch="EXTRA_LAZY")
*@OrderBy({"_ordering" = "ASC"})
        */

        
        protected $coursesections = NULL;

        


    public function __construct() {
        parent::__construct();
        $this->coursequestionansweroptions = new \Doctrine\Common\Collections\ArrayCollection();
$this->coursesections = new \Doctrine\Common\Collections\ArrayCollection();
    }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'course';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'zoeknaam' => array(
                'dbfield' => 'zoeknaam',
                'type' => 'string',
                'ignore' => false,
                'readonly' => true,
            ),
            'name' => array(
                'dbfield' => 'name',
                'type' => 'string',
                'ignore' => false,
            ),
            'language' => array(
                'dbfield' => 'language',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\language',
                'alias' => 'language'
            ),
            'remarks' => array(
                'dbfield' => 'remarks',
                'type' => 'text',
                'ignore' => false,
            ),
            'coursetype' => array(
                'dbfield' => 'coursetype',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\coursetype',
                'alias' => 'coursetype'
            ),
            'number' => array(
                'dbfield' => 'number',
                'type' => 'int',
                'ignore' => false,
            ),
            'active' => array(
                'dbfield' => 'active',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'maxtimeminutes' => array(
                'dbfield' => 'maxtimeminutes',
                'type' => 'int',
                'ignore' => false,
            ),
            'maxattempts' => array(
                'dbfield' => 'maxattempts',
                'type' => 'int',
                'ignore' => false,
            ),
            'successpercentage' => array(
                'dbfield' => 'successpercentage',
                'type' => 'percentage',
                'ignore' => false,
            ),
            'validmonths' => array(
                'dbfield' => 'validmonths',
                'type' => 'int',
                'ignore' => false,
            ),
            'department' => array(
                'dbfield' => 'department',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\department',
                'alias' => 'department'
            ),
            'employee' => array(
                'dbfield' => 'employee',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\employee',
                'alias' => 'employee'
            ),
            'isreadonly' => array(
                'dbfield' => 'isreadonly',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'minimaletijdsduur' => array(
                'dbfield' => 'minimaletijdsduur',
                'type' => 'int',
                'ignore' => false,
            ),
            'coursesectionid' => array(
                'dbfield' => 'coursesectionid',
                'type' => 'int',
                'ignore' => false,
            ),
            'coursequestiontype' => array(
                'dbfield' => 'coursequestiontype',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\coursequestiontype',
                'alias' => 'coursequestiontype'
            ),
            'coursequestionansweroptions' => array(
                'dbfield' => 'coursequestionansweroptions',
                'type' => 'plusbuttongrid',
                'ignore' => false,
                'relation' => 'OneToMany',
                'entity' => '\nl\actinum\custom\tables\coursequestionansweroption',
                'alias' => 'coursequestionansweroptions'
            ),
            'coursesectiontype' => array(
                'dbfield' => 'coursesectiontype',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\coursesectiontype',
                'alias' => 'coursesectiontype'
            ),
            'userpersisted' => array(
                'dbfield' => 'userpersisted',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'settings_showinactive' => array(
                'dbfield' => 'settings_showinactive',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'coursesections' => array(
                'dbfield' => 'coursesections',
                'type' => 'plusbuttongrid',
                'ignore' => false,
                'relation' => 'OneToMany',
                'entity' => '\nl\actinum\custom\tables\coursesection',
                'alias' => 'coursesections'
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "course.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getZoeknaam(){
        return $this->zoeknaam;
    }

    public function setZoeknaam( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'zoeknaam' in setter, should be NULL or STRING");
        }

        $this->zoeknaam = $value;
    }

        
    public function getName(){
        return $this->name;
    }

    public function setName( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'name' in setter, should be NULL or STRING");
        }

        $this->name = $value;
    }

        
    /** @return \nl\actinum\custom\tables\language */
    public function getLanguage(){
        return $this->language;
    }

    public function setLanguage(\nl\actinum\custom\tables\language $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\language || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'language' in setter, should be NULL or instance of \nl\actinum\custom\tables\language");
        }

        $this->language = $value;
    }

        
    public function getRemarks(){
        return $this->remarks;
    }

    public function setRemarks( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'remarks' in setter, should be NULL or STRING");
        }

        $this->remarks = $value;
    }

        
    /** @return \nl\actinum\custom\tables\coursetype */
    public function getCoursetype(){
        return $this->coursetype;
    }

    public function setCoursetype(\nl\actinum\custom\tables\coursetype $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\coursetype || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'coursetype' in setter, should be NULL or instance of \nl\actinum\custom\tables\coursetype");
        }

        $this->coursetype = $value;
    }

        
    public function getNumber(){
        return $this->number;
    }

    public function setNumber( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'number' in setter, should be NULL or INT");
        }

        $this->number = $value;
    }

        
    public function getActive(){
        return $this->active;
    }

    public function setActive( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'active' in setter, should be BOOLEAN");
        }

        $this->active = $value;
    }

        
    public function getMaxtimeminutes(){
        return $this->maxtimeminutes;
    }

    public function setMaxtimeminutes( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'maxtimeminutes' in setter, should be NULL or INT");
        }

        $this->maxtimeminutes = $value;
    }

        
    public function getMaxattempts(){
        return $this->maxattempts;
    }

    public function setMaxattempts( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'maxattempts' in setter, should be NULL or INT");
        }

        $this->maxattempts = $value;
    }

        
    public function getSuccesspercentage(){
        return $this->successpercentage;
    }

    public function setSuccesspercentage( $value){
        
        if (!(is_float($value) || is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'successpercentage' in setter, should be NULL, INT or FLOAT");
        }

        $this->successpercentage = $value;
    }

        
    public function getValidmonths(){
        return $this->validmonths;
    }

    public function setValidmonths( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'validmonths' in setter, should be NULL or INT");
        }

        $this->validmonths = $value;
    }

        
    /** @return \nl\actinum\custom\tables\department */
    public function getDepartment(){
        return $this->department;
    }

    public function setDepartment(\nl\actinum\custom\tables\department $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\department || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'department' in setter, should be NULL or instance of \nl\actinum\custom\tables\department");
        }

        $this->department = $value;
    }

        
    /** @return \nl\actinum\custom\tables\employee */
    public function getEmployee(){
        return $this->employee;
    }

    public function setEmployee(\nl\actinum\custom\tables\employee $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\employee || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'employee' in setter, should be NULL or instance of \nl\actinum\custom\tables\employee");
        }

        $this->employee = $value;
    }

        
    public function getIsreadonly(){
        return $this->isreadonly;
    }

    public function setIsreadonly( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'isreadonly' in setter, should be BOOLEAN");
        }

        $this->isreadonly = $value;
    }

        
    public function getMinimaletijdsduur(){
        return $this->minimaletijdsduur;
    }

    public function setMinimaletijdsduur( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'minimaletijdsduur' in setter, should be NULL or INT");
        }

        $this->minimaletijdsduur = $value;
    }

        
    public function getCoursesectionid(){
        return $this->coursesectionid;
    }

    public function setCoursesectionid( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'coursesectionid' in setter, should be NULL or INT");
        }

        $this->coursesectionid = $value;
    }

        
    /** @return \nl\actinum\custom\tables\coursequestiontype */
    public function getCoursequestiontype(){
        return $this->coursequestiontype;
    }

    public function setCoursequestiontype(\nl\actinum\custom\tables\coursequestiontype $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\coursequestiontype || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'coursequestiontype' in setter, should be NULL or instance of \nl\actinum\custom\tables\coursequestiontype");
        }

        $this->coursequestiontype = $value;
    }

        
    public function getCoursequestionansweroptions(){
        return $this->coursequestionansweroptions;
    }

    public function setCoursequestionansweroptions( $value){
                $this->coursequestionansweroptions = $value;
    }

        
    /** @return \nl\actinum\custom\tables\coursesectiontype */
    public function getCoursesectiontype(){
        return $this->coursesectiontype;
    }

    public function setCoursesectiontype(\nl\actinum\custom\tables\coursesectiontype $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\coursesectiontype || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'coursesectiontype' in setter, should be NULL or instance of \nl\actinum\custom\tables\coursesectiontype");
        }

        $this->coursesectiontype = $value;
    }

        
    public function getUserpersisted(){
        return $this->userpersisted;
    }

    public function setUserpersisted( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'userpersisted' in setter, should be BOOLEAN");
        }

        $this->userpersisted = $value;
    }

        
    public function getSettings_showinactive(){
        return $this->settings_showinactive;
    }

    public function setSettings_showinactive( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'settings_showinactive' in setter, should be BOOLEAN");
        }

        $this->settings_showinactive = $value;
    }

        
    public function getCoursesections(){
        return $this->coursesections;
    }

    public function setCoursesections( $value){
                $this->coursesections = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
);

}
}
?>