<?php
namespace nl\actinum\generated\tables{

class supplierquestionnaireanswer extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
  0 => 'n23022n',
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = false;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
  0 => 'n23031n',
);


        /**
*@ManyToOne(targetEntity="supplierquestionnaire", inversedBy="supplierquestionnaireanswers")
*@JoinColumn(name="supplierquestionnaire_id", referencedColumnName="id")
        */

        
        protected $supplierquestionnaire = NULL;

        
        /**
*@ManyToOne(targetEntity="supplierquestionlist")
        */

        
        protected $supplierquestionlist = NULL;

        
        /**
*@ManyToOne(targetEntity="supplierquestionlistquestion")
        */

        
        protected $supplierquestionlistquestion = NULL;

        
        /**
*@ManyToOne(targetEntity="supplierquestionlistquestionansweroption")
        */

        
        protected $supplierquestionlistquestionansweroption = NULL;

        
        /**
*@Column(type="float", nullable=true)
        */

        
        protected $answernumeric = NULL;

        
        /**
*@Column(type="text", nullable=false)
        */

        
        protected $answertext = '';

        
        /**
*@Column(type="date", nullable=true)
        */

        
        protected $answerdate = NULL;

        
        /**
*@ManyToOne(targetEntity="supplierquestionnaireanswerstate")
        */

        
        protected $supplierquestionnaireanswerstate = NULL;

        
        /**
*@ManyToOne(targetEntity="file")
        */

        
        protected $attachment = NULL;

        


    public function __construct() {
        parent::__construct();
            }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'supplierquestionnaireanswer';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'supplierquestionnaire' => array(
                'dbfield' => 'supplierquestionnaire',
                'type' => 'parentreference',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\supplierquestionnaire',
                'alias' => 'supplierquestionnaire'
            ),
            'supplierquestionlist' => array(
                'dbfield' => 'supplierquestionlist',
                'type' => 'search',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\supplierquestionlist',
                'alias' => 'supplierquestionlist'
            ),
            'supplierquestionlistquestion' => array(
                'dbfield' => 'supplierquestionlistquestion',
                'type' => 'search',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\supplierquestionlistquestion',
                'alias' => 'supplierquestionlistquestion'
            ),
            'supplierquestionlistquestionansweroption' => array(
                'dbfield' => 'supplierquestionlistquestionansweroption',
                'type' => 'search',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\supplierquestionlistquestionansweroption',
                'alias' => 'supplierquestionlistquestionansweroption'
            ),
            'answernumeric' => array(
                'dbfield' => 'answernumeric',
                'type' => 'float',
                'ignore' => false,
            ),
            'answertext' => array(
                'dbfield' => 'answertext',
                'type' => 'text',
                'ignore' => false,
            ),
            'answerdate' => array(
                'dbfield' => 'answerdate',
                'type' => 'date',
                'ignore' => false,
            ),
            'supplierquestionnaireanswerstate' => array(
                'dbfield' => 'supplierquestionnaireanswerstate',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\supplierquestionnaireanswerstate',
                'alias' => 'supplierquestionnaireanswerstate'
            ),
            'attachment' => array(
                'dbfield' => 'attachment',
                'type' => 'file',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\file',
                'alias' => 'attachment'
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "supplierquestionnaireanswer.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getSupplierquestionnaire(){
        return $this->supplierquestionnaire;
    }

    public function setSupplierquestionnaire( $value){
                $this->supplierquestionnaire = $value;
    }

        
    /** @return \nl\actinum\custom\tables\supplierquestionlist */
    public function getSupplierquestionlist(){
        return $this->supplierquestionlist;
    }

    public function setSupplierquestionlist(\nl\actinum\custom\tables\supplierquestionlist $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\supplierquestionlist || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'supplierquestionlist' in setter, should be NULL or instance of \nl\actinum\custom\tables\supplierquestionlist");
        }

        $this->supplierquestionlist = $value;
    }

        
    /** @return \nl\actinum\custom\tables\supplierquestionlistquestion */
    public function getSupplierquestionlistquestion(){
        return $this->supplierquestionlistquestion;
    }

    public function setSupplierquestionlistquestion(\nl\actinum\custom\tables\supplierquestionlistquestion $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\supplierquestionlistquestion || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'supplierquestionlistquestion' in setter, should be NULL or instance of \nl\actinum\custom\tables\supplierquestionlistquestion");
        }

        $this->supplierquestionlistquestion = $value;
    }

        
    /** @return \nl\actinum\custom\tables\supplierquestionlistquestionansweroption */
    public function getSupplierquestionlistquestionansweroption(){
        return $this->supplierquestionlistquestionansweroption;
    }

    public function setSupplierquestionlistquestionansweroption(\nl\actinum\custom\tables\supplierquestionlistquestionansweroption $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\supplierquestionlistquestionansweroption || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'supplierquestionlistquestionansweroption' in setter, should be NULL or instance of \nl\actinum\custom\tables\supplierquestionlistquestionansweroption");
        }

        $this->supplierquestionlistquestionansweroption = $value;
    }

        
    public function getAnswernumeric(){
        return $this->answernumeric;
    }

    public function setAnswernumeric( $value){
        
        //convert to proper float to prevent the entitychangelogger to be triggered.
        if (!(is_float($value) || is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'answernumeric' in setter, should be NULL, INT or FLOAT");
        }

        $this->answernumeric = $value;
    }

        
    public function getAnswertext(){
        return $this->answertext;
    }

    public function setAnswertext( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'answertext' in setter, should be NULL or STRING");
        }

        $this->answertext = $value;
    }

        
    /** @return \DateTime */
    public function getAnswerdate(){
        return $this->answerdate;
    }

    public function setAnswerdate(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'answerdate' in setter, should be NULL or instance of \DateTime");
        }

        $this->answerdate = $value;
    }

        
    /** @return \nl\actinum\custom\tables\supplierquestionnaireanswerstate */
    public function getSupplierquestionnaireanswerstate(){
        return $this->supplierquestionnaireanswerstate;
    }

    public function setSupplierquestionnaireanswerstate(\nl\actinum\custom\tables\supplierquestionnaireanswerstate $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\supplierquestionnaireanswerstate || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'supplierquestionnaireanswerstate' in setter, should be NULL or instance of \nl\actinum\custom\tables\supplierquestionnaireanswerstate");
        }

        $this->supplierquestionnaireanswerstate = $value;
    }

        
    /** @return \nl\actinum\custom\tables\file */
    public function getAttachment(){
        return $this->attachment;
    }

    public function setAttachment(\nl\actinum\custom\tables\file $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\file || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'attachment' in setter, should be NULL or instance of \nl\actinum\custom\tables\file");
        }
        $this->attachment = $value;
    }

        
    protected $filefields = array (
  0 => 'attachment',
);

    protected $filemultiplefields = array (
);

}
}
?>