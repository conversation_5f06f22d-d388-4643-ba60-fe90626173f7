<?php
namespace nl\actinum\generated\tables{

class companycontactquality extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = false;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
);


        /**
*@ManyToOne(targetEntity="company", inversedBy="contactquality")
*@JoinColumn(name="company_id", referencedColumnName="id")
        */

        
        protected $company = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $name = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $email = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $phone = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $mobile = '';

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $suppliercontact = false;

        


    public function __construct() {
        parent::__construct();
            }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'companycontactquality';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'company' => array(
                'dbfield' => 'company',
                'type' => 'plusbuttonform',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\company',
                'alias' => 'company'
            ),
            'name' => array(
                'dbfield' => 'name',
                'type' => 'string',
                'ignore' => false,
            ),
            'email' => array(
                'dbfield' => 'email',
                'type' => 'string',
                'ignore' => false,
            ),
            'phone' => array(
                'dbfield' => 'phone',
                'type' => 'string',
                'ignore' => false,
            ),
            'mobile' => array(
                'dbfield' => 'mobile',
                'type' => 'string',
                'ignore' => false,
            ),
            'suppliercontact' => array(
                'dbfield' => 'suppliercontact',
                'type' => 'yesno',
                'ignore' => false,
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "companycontactquality.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getCompany(){
        return $this->company;
    }

    public function setCompany( $value){
                $this->company = $value;
    }

        
    public function getName(){
        return $this->name;
    }

    public function setName( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'name' in setter, should be NULL or STRING");
        }

        $this->name = $value;
    }

        
    public function getEmail(){
        return $this->email;
    }

    public function setEmail( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'email' in setter, should be NULL or STRING");
        }

        $this->email = $value;
    }

        
    public function getPhone(){
        return $this->phone;
    }

    public function setPhone( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'phone' in setter, should be NULL or STRING");
        }

        $this->phone = $value;
    }

        
    public function getMobile(){
        return $this->mobile;
    }

    public function setMobile( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'mobile' in setter, should be NULL or STRING");
        }

        $this->mobile = $value;
    }

        
    public function getSuppliercontact(){
        return $this->suppliercontact;
    }

    public function setSuppliercontact( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'suppliercontact' in setter, should be BOOLEAN");
        }

        $this->suppliercontact = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
);

}
}
?>