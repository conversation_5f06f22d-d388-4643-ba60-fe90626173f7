<?php
namespace nl\actinum\generated\tables{

class registratieregelantwoordoptie extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = true;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
);


        /**
*@ManyToOne(targetEntity="registratieformulier", inversedBy="registratieregelantwoordopties")
*@JoinColumn(name="registratieformulier_id", referencedColumnName="id", onDelete="CASCADE")
        */

        
        protected $registratieformulier = NULL;

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $_ordering = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $optionlistoption = '';

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $actionrequired = false;

        
        /**
*@ManyToOne(targetEntity="registratieregel", inversedBy="registratieregelantwoordopties")
*@JoinColumn(name="registratieregel_id", referencedColumnName="id", onDelete="CASCADE")
        */

        
        protected $registratieregel = NULL;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $notapplicable = false;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $norm = false;

        


    public function __construct() {
        parent::__construct();
            }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'registratieregelantwoordoptie';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'registratieformulier' => array(
                'dbfield' => 'registratieformulier',
                'type' => 'parentreference',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\registratieformulier',
                'alias' => 'registratieformulier'
            ),
            'optionlistoption' => array(
                'dbfield' => 'optionlistoption',
                'type' => 'string',
                'ignore' => false,
            ),
            'actionrequired' => array(
                'dbfield' => 'actionrequired',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'registratieregel' => array(
                'dbfield' => 'registratieregel',
                'type' => 'parentreference',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\registratieregel',
                'alias' => 'registratieregel'
            ),
            'notapplicable' => array(
                'dbfield' => 'notapplicable',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'norm' => array(
                'dbfield' => 'norm',
                'type' => 'yesno',
                'ignore' => false,
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "registratieregelantwoordoptie.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getRegistratieformulier(){
        return $this->registratieformulier;
    }

    public function setRegistratieformulier( $value){
                $this->registratieformulier = $value;
    }

        
    public function get_ordering(){
        return $this->_ordering;
    }

    public function set_ordering( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_ordering' in setter, should be NULL or INT");
        }

        $this->_ordering = $value;
    }

        
    public function getOptionlistoption(){
        return $this->optionlistoption;
    }

    public function setOptionlistoption( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'optionlistoption' in setter, should be NULL or STRING");
        }

        $this->optionlistoption = $value;
    }

        
    public function getActionrequired(){
        return $this->actionrequired;
    }

    public function setActionrequired( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'actionrequired' in setter, should be BOOLEAN");
        }

        $this->actionrequired = $value;
    }

        
    public function getRegistratieregel(){
        return $this->registratieregel;
    }

    public function setRegistratieregel( $value){
                $this->registratieregel = $value;
    }

        
    public function getNotapplicable(){
        return $this->notapplicable;
    }

    public function setNotapplicable( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'notapplicable' in setter, should be BOOLEAN");
        }

        $this->notapplicable = $value;
    }

        
    public function getNorm(){
        return $this->norm;
    }

    public function setNorm( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'norm' in setter, should be BOOLEAN");
        }

        $this->norm = $value;
    }

        
    protected $filefields = array (
);

    protected $filemultiplefields = array (
);

}
}
?>