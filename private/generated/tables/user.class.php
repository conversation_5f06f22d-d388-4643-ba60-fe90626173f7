<?php
namespace nl\actinum\generated\tables{

class user extends \nl\actinum\framework\application\DbTable {


        public static $gridspointingtothistable = array (
);

        public static $API_getPartials_called_list = array();

        public static $hasNativeOrdering = true;

        public static $API_getTranslations_called_list = array();

        public static $API_getAssociations_called_list = array();


        public static $tablepagespointingtothistable = array (
  0 => 'n6548n',
);


        /**
*@Column(type="string", unique=true, nullable=false)
        */

        
        protected $username = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $password = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $salt = '';

        
        /**
*@Column(type="string", nullable=true)
        */

        
        protected $_smsauth = '';

        
        /**
*@Column(type="string", nullable=true)
        */

        
        protected $_tokenauth = '';

        
        /**
*@Column(type="string", nullable=true)
        */

        
        protected $_totpsecret = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $openid = '';

        
        /**
*@Column(type="boolean", name="active", nullable=false, columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $active = false;

        
        /**
*@Column(type="boolean", name="anonymous", nullable=false, columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $anonymous = false;

        
        /**
*@ManyToOne(targetEntity="role")
        */

        
        protected $role = NULL;

        
        /**
*@ManyToMany(targetEntity="role")
*@JoinTable(name="user_role_roles", joinColumns={@JoinColumn(name="user_id", referencedColumnName="id", onDelete="CASCADE")},inverseJoinColumns={@JoinColumn(name="role_id", referencedColumnName="id")})
        */

        
        protected $roles = NULL;

        
        /**
*@ManyToOne(targetEntity="file")
        */

        
        protected $userphoto = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $zoeknaam = '';

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $email = '';

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $_ordering = NULL;

        
        /**
*@Column(type="boolean", nullable=false,  default="0", columnDefinition="TINYINT( 1 ) NOT NULL DEFAULT  '0'")
        */

        
        protected $ismainuser = false;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $apptoken = '';

        
        /**
*@Column(type="integer", nullable=true)
        */

        
        protected $dpsnumber = NULL;

        
        /**
*@Column(type="string", nullable=false)
        */

        
        protected $third_party_id = '';

        
        /**
*@Column(type="date", nullable=true)
        */

        
        protected $hired_since = NULL;

        
        /**
*@Column(type="date", nullable=true)
        */

        
        protected $hired_until = NULL;

        
        /**
*@Column(type="datetimetz", nullable=true)
        */

        
        protected $last_login = NULL;

        


    public function __construct() {
        parent::__construct();
        $this->roles = new \Doctrine\Common\Collections\ArrayCollection();
    }

    protected function getDefaultEntries(){
        $queries = array();
                return $queries;
    }

    public static function API_getAlias(){
        return 'user';
    }

    //valid fields that we are allowed to get and set
    public static function getApiFieldMapping(){
        $mapping = array(
        
            'username' => array(
                'dbfield' => 'username',
                'type' => 'string',
                'ignore' => false,
            ),
            'password' => array(
                'dbfield' => 'password',
                'type' => 'string',
                'ignore' => false,
            ),
            'salt' => array(
                'dbfield' => 'salt',
                'type' => 'string',
                'ignore' => false,
            ),
            '_smsauth' => array(
                'dbfield' => '_smsauth',
                'type' => 'string',
                'ignore' => false,
            ),
            '_tokenauth' => array(
                'dbfield' => '_tokenauth',
                'type' => 'string',
                'ignore' => false,
            ),
            '_totpsecret' => array(
                'dbfield' => '_totpsecret',
                'type' => 'string',
                'ignore' => false,
            ),
            'openid' => array(
                'dbfield' => 'openid',
                'type' => 'string',
                'ignore' => false,
            ),
            'active' => array(
                'dbfield' => 'active',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'anonymous' => array(
                'dbfield' => 'anonymous',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'role' => array(
                'dbfield' => 'role',
                'type' => 'dropdown',
                'ignore' => false,
                'relation' => 'ManyToOne',
                'entity' => '\nl\actinum\custom\tables\role',
                'alias' => 'role'
            ),
            'roles' => array(
                'dbfield' => 'roles',
                'type' => 'linkform',
                'ignore' => false,
                'relation' => 'ManyToMany',
                'entity' => '\nl\actinum\custom\tables\role',
                'alias' => 'roles'
            ),
            'userphoto' => array(
                'dbfield' => 'userphoto',
                'type' => 'file',
                'ignore' => false,
            ),
            'zoeknaam' => array(
                'dbfield' => 'zoeknaam',
                'type' => 'string',
                'ignore' => false,
                'readonly' => true,
            ),
            'email' => array(
                'dbfield' => 'email',
                'type' => 'string',
                'ignore' => false,
            ),
            'ismainuser' => array(
                'dbfield' => 'ismainuser',
                'type' => 'yesno',
                'ignore' => false,
            ),
            'apptoken' => array(
                'dbfield' => 'apptoken',
                'type' => 'string',
                'ignore' => false,
            ),
            'dpsnumber' => array(
                'dbfield' => 'dpsnumber',
                'type' => 'int',
                'ignore' => false,
            ),
            'third_party_id' => array(
                'dbfield' => 'third_party_id',
                'type' => 'string',
                'ignore' => false,
            ),
            'hired_since' => array(
                'dbfield' => 'hired_since',
                'type' => 'date',
                'ignore' => false,
            ),
            'hired_until' => array(
                'dbfield' => 'hired_until',
                'type' => 'date',
                'ignore' => false,
            ),
            'last_login' => array(
                'dbfield' => 'last_login',
                'type' => 'datetime',
                'ignore' => false,
            ),
        );

        $mapping = array_merge(parent::getApiFieldMapping(), $mapping);
        return $mapping;
    }


    /**
    * Creates an entity
    *
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @example [{"apitoken" : "#APITOKEN#", "method" : "user.create", "params" : [{"onderwerp" : "hoi"}], "id" : 1}]
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_create($fields){
        return parent::API_create($fields);
    }

    /**
    * Gets a set of entities
    *
    * @param filters An array of filter-objects (optional, see General Help)
    * @param options An object of options (optional, see General Help)
    * @return array of found entities
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_get($filters = array(), $options = array(), $maxdepth = 2){
        return parent::API_get($filters, $options, $maxdepth);
    }


    /**
    * Updates an entity
    *
    * @param id The ID of the entity to update (required)
    * @param fields an object of key values pairs (required, see example. Not all keys are required!)
    * @return true on success, or an error when something goes wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_update($id, $fields){
        return parent::API_update($id, $fields);
    }


    /**
    * Removes an entity
    *
    * @param id The ID of the entity (required)
    * @return true on success, or an error when something is wrong.
    * @version 1
    * @\nl\actinum\framework\application\annotations\allowedFromApi
    */
    public static function API_delete($id){
        return parent::API_delete($id);
    }



    public function getUsername(){
        return $this->username;
    }

    public function setUsername( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'username' in setter, should be NULL or STRING");
        }

        $this->username = $value;
    }

        
    public function getPassword(){
        return $this->password;
    }

    public function setPassword( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'password' in setter, should be NULL or STRING");
        }

        $this->password = $value;
    }

        
    public function getSalt(){
        return $this->salt;
    }

    public function setSalt( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'salt' in setter, should be NULL or STRING");
        }

        $this->salt = $value;
    }

        
    public function get_smsauth(){
        return $this->_smsauth;
    }

    public function set_smsauth( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_smsauth' in setter, should be NULL or STRING");
        }

        $this->_smsauth = $value;
    }

        
    public function get_tokenauth(){
        return $this->_tokenauth;
    }

    public function set_tokenauth( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_tokenauth' in setter, should be NULL or STRING");
        }

        $this->_tokenauth = $value;
    }

        
    public function get_totpsecret(){
        return $this->_totpsecret;
    }

    public function set_totpsecret( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_totpsecret' in setter, should be NULL or STRING");
        }

        $this->_totpsecret = $value;
    }

        
    public function getOpenid(){
        return $this->openid;
    }

    public function setOpenid( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'openid' in setter, should be NULL or STRING");
        }

        $this->openid = $value;
    }

        
    public function getActive(){
        return $this->active;
    }

    public function setActive( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'active' in setter, should be BOOLEAN");
        }

        $this->active = $value;
    }

        
    public function getAnonymous(){
        return $this->anonymous;
    }

    public function setAnonymous( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'anonymous' in setter, should be BOOLEAN");
        }

        $this->anonymous = $value;
    }

        
    /** @return \nl\actinum\custom\tables\role */
    public function getRole(){
        return $this->role;
    }

    public function setRole(\nl\actinum\custom\tables\role $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\role || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'role' in setter, should be NULL or instance of \nl\actinum\custom\tables\role");
        }

        $this->role = $value;
    }

        
    public function getRoles(){
        return $this->roles;
    }

    public function setRoles( $value){
                $this->roles = $value;
    }

        
    /** @return \nl\actinum\custom\tables\file */
    public function getUserphoto(){
        return $this->userphoto;
    }

    public function setUserphoto(\nl\actinum\custom\tables\file $value = null){
        
        if (!($value instanceOf \nl\actinum\custom\tables\file || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'userphoto' in setter, should be NULL or instance of \nl\actinum\custom\tables\file");
        }
        $this->userphoto = $value;
    }

        
    public function getZoeknaam(){
        return $this->zoeknaam;
    }

    public function setZoeknaam( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'zoeknaam' in setter, should be NULL or STRING");
        }

        $this->zoeknaam = $value;
    }

        
    public function getEmail(){
        return $this->email;
    }

    public function setEmail( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'email' in setter, should be NULL or STRING");
        }

        $this->email = $value;
    }

        
    public function get_ordering(){
        return $this->_ordering;
    }

    public function set_ordering( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field '_ordering' in setter, should be NULL or INT");
        }

        $this->_ordering = $value;
    }

        
    public function getIsmainuser(){
        return $this->ismainuser;
    }

    public function setIsmainuser( $value){
        
        if (!(is_bool($value))){
            throw new \Exception("Invalid value supplied for field 'ismainuser' in setter, should be BOOLEAN");
        }

        $this->ismainuser = $value;
    }

        
    public function getApptoken(){
        return $this->apptoken;
    }

    public function setApptoken( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'apptoken' in setter, should be NULL or STRING");
        }

        $this->apptoken = $value;
    }

        
    public function getDpsnumber(){
        return $this->dpsnumber;
    }

    public function setDpsnumber( $value){
        
        if (!(is_int($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'dpsnumber' in setter, should be NULL or INT");
        }

        $this->dpsnumber = $value;
    }

        
    public function getThird_party_id(){
        return $this->third_party_id;
    }

    public function setThird_party_id( $value){
        
        if (!(is_string($value) || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'third_party_id' in setter, should be NULL or STRING");
        }

        $this->third_party_id = $value;
    }

        
    /** @return \DateTime */
    public function getHired_since(){
        return $this->hired_since;
    }

    public function setHired_since(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'hired_since' in setter, should be NULL or instance of \DateTime");
        }

        $this->hired_since = $value;
    }

        
    /** @return \DateTime */
    public function getHired_until(){
        return $this->hired_until;
    }

    public function setHired_until(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'hired_until' in setter, should be NULL or instance of \DateTime");
        }

        $this->hired_until = $value;
    }

        
    /** @return \DateTime */
    public function getLast_login(){
        return $this->last_login;
    }

    public function setLast_login(\DateTime $value = null){
        
        if (!($value instanceOf \DateTime || is_null($value))){
            throw new \Exception("Invalid value supplied for field 'last_login' in setter, should be NULL or instance of \DateTime");
        }

        $this->last_login = $value;
    }

        
    protected $filefields = array (
  0 => 'userphoto',
);

    protected $filemultiplefields = array (
);

}
}
?>