<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="google" value="notranslate" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge; chrome=1">
        <meta name="viewport" content="width=device-width, maximum-scale=1, user-scalable=no" />
        <title><?php echo $title; ?></title>
        <link id="favicon" rel="shortcut Icon" href="favicon.ico" type="image/x-icon" />
        <link rel="apple-touch-icon" href="favicon.ico"/>
        <link href="<?php echo $cssgeneratedrelativefilename; ?>" type="text/css" rel="stylesheet" media="screen, print" />
        <style>
            <?php echo $dynamicCssString; ?>
        </style>

        <!--[if lte IE 10]>
        <link href="assets/css/ie.css" type="text/css" rel="stylesheet" media="screen" />
        <![endif]-->
        <link href="assets/css/webkit.css" type="text/css" rel="stylesheet" media="screen" />


        <link rel="stylesheet" media="print" href="assets/css/print.css">

        <?php
        if(isset($locale)) {
            echo <<<EOF
<link rel="gettext" type="application/x-po" href="locales/${locale}.po" />
<script>LOCALE = '${locale}';</script>
EOF;
        }
        ?>

    </head>

    <body style="display:  none;">


        <div id="actinum_webapp"></div>

        <div id="wrapper">
            <div id="superheader">
                <div id="inner" class="inner">
                    <ul id="modules" class="gnav">
                        <!-- dynamically add the modulelinks here -->
                    </ul>

                    <nav>
                        <ul style="float: right;">
                            <li>
                                <?php echo __('Ingelogd als'); ?>: <span id="loggedinas"></span>
                            </li>
                            <?php
                                if ($shortenedurls){
                                    echo '
                                    <li>
                                        <a style="padding-left: 10px;" href="javascript: Actinum.application.createShortenedUrl();">
                                            <img src="assets/images/link.png" alt="'. __('Link afbeelding') . '" title="' . __('Maak een ShortLink aan') .'" />
                                        </a>
                                    </li>
                                    ';
                                }
                            ?>
                            <li>
                                <a style="padding-left: 10px;" href="javascript: void(0);">
                                    <img src="assets/images/settings.png" alt="<?php echo __('Tandwiel afbeelding'); ?>" title="<?php echo __('Opties'); ?>" />
                                </a>
                                <ul id="user_nav">

                                    <li>
                                        <hr style="border: 0px;height: 1px;background-color: white;" />
                                    </li>

                                    <li style="<?php echo $helpenabled ? '' : 'display: none;'; ?>">
                                        <a target="_blank" href="help.php"><?php echo __('Help'); ?></a>
                                    </li>

                                    <li style="<?php echo $helpenabled ? '' : 'display: none;'; ?>">
                                        <a target="_blank" href="updates.php"><?php echo __('Updates'); ?></a>
                                    </li>

                                    <li style="<?php echo $feedbackenabled ? '' : 'display: none;'; ?>">
                                        <a target="_blank" href="http://support.whiteworks.nl/customer/portal/emails/new"><?php echo __('Geef feedback!'); ?></a>
                                    </li>

                                    <li style="<?php echo $tutorialsenabled ? '' : 'display: none;'; ?>">
                                        <a href="javascript:Actinum.tutorialwizard.show();"><?php echo __('Interactieve tutorials'); ?></a>
                                    </li>
                                    <li>
                                        <a id="logoutlink" href="javascript: Actinum.application.logout();" class="welcome-login"><?php echo __('Uitloggen'); ?></a>
                                    </li>

                                </ul>
                            </li>
                        </ul>
                    </nav>

                </div>
            </div>


          
            <div id="header">

                <?php
                $date = date('m-d');
                switch ($date){
                    case '12-05': //sinterklaas
                        $logo_filename = 'logo_app_sinterklaas.png';
                        break;
                    case '12-25': //kerst
                    case '12-26': //kerst
                        $logo_filename = 'logo_app_kerst.png';
                        break;
                    default:
                        $logo_filename = 'logo_app.png';
                        break;
                }

                if (strpos($_SERVER['HTTP_HOST'], '.i-self.eu') !== false){
                    $logo_filename = 'logo_kpc_app.png';
                }

                if (!file_exists($logo_filename)){
                    $logo_filename = 'logo_app.png'; //default fallback voor systemen die geen custom logo's hebben.
                }
                echo '<a href="javascript: Actinum.application.loadStartPage();"><img src="'.$logo_filename.'" id="logo" height="55" width="240" title="'.$title.'" /></a>';
                ?>

                <div id="systemnotifications" style="width: 400px;">
                </div>

                <div id="search" style="<?php echo $globalsearchenabled ? '' : 'display: none;'; ?>">
                    <form action="" method="get" onsubmit="return false;">
                        <input autocomplete="off" placeholder="<?php echo __('WhiteWorks doorzoeken'); ?>" style="width: 240px;" class="searchfield" id="searchinput" name="q" type="search" />
                    </form>
                    <div id="searchresults" style="display: none;">
                    </div>
                </div>



            </div>

            <div class="clear"></div>



           

            <div class="clear"></div>


            <div id="content">

                <div id="adminbar">
                    <ul id="pagegroup_link_container" class="nav clearfix">
                        <!-- dynamically add the pagegrouplinks here -->
                    </ul>
                </div>

               <div id="pagelinks">
                    <div id="page_link_container">
                        <!-- dynamically add the pagelinks here -->
                    </div>
                </div>

                <div class="flash-messages"></div>

                <div class="clear"></div>
                
                <div id="breadcrumbstrail">
                </div>

                <div class="clear"></div>


                <div id="currentsubpage">
                </div>

            </div>

            <div class="clear"></div>

            <div id="footer">
                <p class="copyleft">
                <?php
                if (!$disablecopyright){
                    $notice = sprintf( __('%s is geproduceerd door %s'), $title, '<a target="_blank" href="http://www.whiteworks.nl/">WhiteWorks</a>' );
                    ?>
                    <?php echo $notice; ?> © 2007 - <?php echo date('Y'); ?> <?php echo '('.$version.'.'.$build.')'; ?>
                    <?php
                }
                if ($affiliate){
                    ?>
                    Korting op je factuur? <a href="javascript: $N('n2n').showAffiliatePopup();"> Beveel WhiteWorks aan bij mede-ondernemers!</a>
                    <?php
                }
                ?>
                </p>
            </div>

            <div id="loadingindicator" style="display: none;"><?php echo __('Laden...'); ?></div>

            <iframe name="downloadiframe" id="downloadiframe" style="display: none;">
            </iframe>

        </div>

        <div id="messagecontainer">
        </div>

        <script type="text/javascript" src="<?php echo $jsgeneratedrelativefilename.'?'.$jsbuild; ?>"></script>

        <script type="text/javascript">
Actinum.Plugins = [];
Actinum.systemNotifications = <?php echo $systemNotifications; ?>;
Actinum.username = "<?php echo $username; ?>";
Actinum.debugmode = <?php echo $debugmode ? 'true' : 'false'; ?>;
Actinum.validtutorials = <?php echo $validtutorialids; ?>;
Actinum.ignoremaintenance = <?php echo $ignoremaintenance; ?>;
Actinum.profile_counter = 1;
<?php
foreach($systemVariables as $key => $value){
    echo 'Actinum.'.$key.' = '.json_encode($value).';
';
}
?>

        </script>

        <script type="text/javascript">
//function waitForMooTools(){
//    if(typeof MooTools === 'undefined') {
//        setTimeout(waitForMooTools, 1000);
//    } else {
        window.addEvent('domready', function() {
//            try{
                Actinum.application = new Actinum.Application.Application_desktop();
                Actinum.application.setCachediscriminator("<?php echo $cachediscriminator; ?>");
                Actinum.application.setUid("<?php echo $useruid; ?>");
                Actinum.application.setMenuStructure(<?php echo $appstructure; ?>);
                Actinum.application.run();
//            }
//            catch(e){
//                console.error(e.message);
//                console.trace();
//            }
        });
//    }
//}
//waitForMooTools();
        </script>

<!--        <script type="text/javascript">-->
<!---->
<!--            //application tracker-->
<!--            var _gaq = _gaq || [];-->
<!--            _gaq.push(['applicationTracker._setAccount', 'UA-********-2']);-->
<!--//            _gaq.push(['applicationTracker._setDomainName', 'whiteworks.nl']);-->
<!--//            _gaq.push(['applicationTracker._setAllowLinker', true]);-->
<!--            _gaq.push(['applicationTracker._trackPageview']);-->
<!---->
<!--            //website+application tracker-->
<!--            _gaq.push(['websiteAndApplicationTracker._setAccount', 'UA-********-4']);-->
<!--            _gaq.push(['websiteAndApplicationTracker._setDomainName', 'whiteworks.nl']);-->
<!--            _gaq.push(['websiteAndApplicationTracker._setAllowLinker', true]);-->
<!--            _gaq.push(['websiteAndApplicationTracker._trackPageview']);-->
<!---->
<!--            (function() {-->
<!--                var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;-->
<!--                ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';-->
<!--                var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);-->
<!--            })();-->
<!---->
<!--        </script>-->

        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-R2S5DKX286"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'G-R2S5DKX286');
        </script>


        <?php
        foreach($pluginjs as $p){
            echo '<script type="text/javascript" src="'.$p.'"></script>';
        }
        foreach($plugincss as $p){
            echo '<link rel="stylesheet" href="'.$p.'" type="text/chrome/safari" />';
        }
        ?>


    </body>
</html>
