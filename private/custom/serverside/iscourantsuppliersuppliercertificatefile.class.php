<?php
namespace nl\actinum\framework\custom\application{

    use \Doctrine\ORM\Query\AST\Functions\FunctionNode;
    use \Doctrine\ORM\Query\Lexer;

    class iscourantsuppliersuppliercertificatefile extends FunctionNode{

        public $suppliercertificatefile_alias;

        public function getSql(\Doctrine\ORM\Query\SqlWalker $sqlWalker)
        {

            $suppliercertificatefile_alias = ($sqlWalker->walkSimpleArithmeticExpression($this->suppliercertificatefile_alias));
            list($suppliercertificatefile_table_alias, $idfrag) = explode('.', $suppliercertificatefile_alias);

            return '(
                (
                    SELECT id
                    FROM suppliersuppliercertificatefile ssf2
                    WHERE ssf2.suppliersuppliercertificate_id = '.$suppliercertificatefile_table_alias.'.suppliersuppliercertificate_id
                    ORDER BY ssf2.validuntil DESC
                    LIMIT 1
                ) = '.$suppliercertificatefile_alias.'
            )';
        }


        public function parse(\Doctrine\ORM\Query\Parser $parser)
        {
            $lexer = $parser->getLexer();

            $parser->match(Lexer::T_IDENTIFIER);
            $parser->match(Lexer::T_OPEN_PARENTHESIS);
            $this->suppliercertificatefile_alias = $parser->SimpleArithmeticExpression();
            $parser->match(Lexer::T_CLOSE_PARENTHESIS);
        }

    }

}
?>