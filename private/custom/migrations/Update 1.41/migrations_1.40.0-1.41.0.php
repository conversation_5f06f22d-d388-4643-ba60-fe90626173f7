<?php
$configkey = 'staging';
set_time_limit(0);

$logfile = '/tmp/migrations_1.40.0-1.41.0_'.date('Y-m-d_H-i-s').'.log';

$xmlstring = file_get_contents('../../configs/'.$configkey.'.xml');
$xml = simplexml_load_string($xmlstring);
$json = json_encode($xml);
$config_array = json_decode($json,TRUE);

$host = $config_array[$configkey]['databases']['shards']['shard']['host'];
$user = $config_array[$configkey]['databases']['shards']['shard']['username'];
$pass = $config_array[$configkey]['databases']['shards']['shard']['password'];

$tenant_start = 10;
$tenant_stop = 10;

$link = mysqli_connect($host, $user, $pass);

$queries = array(
    'SET NAMES utf8;',

    // attach al tags to the default systemsettings record
    'UPDATE tag SET systemsettings_id = 1 WHERE systemsettings_id IS NULL;',

    // set cascade so tags will be deleted from document when the tag is deleted from the systemsettings
    'ALTER TABLE document_tags_tag DROP FOREIGN KEY FK_2B1D22938D7B4FB4;',
    'ALTER TABLE document_tags_tag ADD CONSTRAINT FK_2B1D22938D7B4FB4 FOREIGN KEY (tags_id) REFERENCES tag (id) ON DELETE CASCADE;',
);


for($i = $tenant_start; $i <= $tenant_stop; $i++){
    $start = microtime(true);
    $db = 'actinum_studiov3_project_111_tenant'.$i;

    mysqli_select_db($link, $db);
    if (mysqli_errno($link)) {
        logMessage("MYSQL ERROR: ".mysqli_errno($link).' - '.mysqli_error($link)."\n");
        continue;
    }

    logMessage('Updating '.$db.'... ');
    try{
        mysqli_query_with_log($link, 'START TRANSACTION');

        foreach($queries as $query){
            mysqli_query($link, $query);
            if (mysqli_errno($link)) {
                logMessage("MYSQL ERROR: ".mysqli_errno($link).' - '.mysqli_error($link)."\n");
                logMessage("ON LINE ".$i.": ".$query);
            }
        }

        mysqli_query_with_log($link, 'COMMIT;');
    }
    catch(Exception $e){
        logMessage('Error: '.$e->getMessage());
        mysqli_query_with_log($link, 'ROLLBACK;');
    }
    $stop = microtime(true);
    $dur = round($stop - $start, 2);
    logMessage('DONE in '.$dur.'s.'."\n");
}

function mysqli_query_with_log($link, $query){
    $result = mysqli_query($link, $query);
    if (mysqli_errno($link)) {
        logMessage("MYSQL ERROR: ".mysqli_errno($link).' - '.mysqli_error($link)."\n");
        logMessage("  ==> query was: ".$query."\n");
    }
    return $result;
}

function logMessage($s){
    global $logfile;
    $handle = fopen($logfile, 'a');
    fwrite($handle,$s);
    echo $s;
    fclose($handle);
}
?>
