<?php
//die('disabled');

$configkey = 'staging';
set_time_limit(0);
//updatescript runnen (/usr/local/zend/bin/php migrations_....php

//$logfile = '/tmp/migrations_6.16-6.17.1_'.date('Y-m-d_H-i-s').'_tenant{dbid}.log';
$logfile = '/tmp/migrations_1.23.1-1.23.2_'.date('Y-m-d_H-i-s').'.log';

$xmlstring = file_get_contents('../../configs/'.$configkey.'.xml');
$xml = simplexml_load_string($xmlstring);
$json = json_encode($xml);
$config_array = json_decode($json,TRUE);

$host = $config_array[$configkey]['databases']['shards']['shard']['host'];
$user = $config_array[$configkey]['databases']['shards']['shard']['username'];
$pass = $config_array[$configkey]['databases']['shards']['shard']['password'];

//set_time_limit(0);
//
//
//$logfile = '/tmp/migrations_1.22.7-1.22.8_'.date('Y-m-d_H-i-s').'.log';
//
//$host = 'localhost';
//$user = 'root';
//$pass = 'rootroot';

$tenant_start = 1;
$tenant_stop = 1;

$link = mysqli_connect($host, $user, $pass);

$queries = array(
    //altijd!
    'SET NAMES utf8;',

    // handboek notificatie leeg maken als hier content van een cursus in staat
    'UPDATE systemsettings SET documentrejectnotificationsubject = \'\' WHERE documentrejectnotificationsubject LIKE \'%{cursus}%\';',
    'UPDATE systemsettings SET documentrejectnotificationcontent = \'\' WHERE documentrejectnotificationcontent LIKE \'%{cursus}%\';',
    'UPDATE systemsettings SET documentrejectnotification = FALSE WHERE documentrejectnotificationsubject = \'\' AND documentrejectnotificationcontent = \'\';',

    // alle rechten waarvan de parent niet actief is ook op inactief zetten
    'UPDATE profile a JOIN profile b ON a._parent_id = b.id SET a.active = b.active',

    // recht hernoemen voor het exporteren van ingredienten per grondstof
    'UPDATE profile SET name = \'Exporteer ingrediënten per grondstof\', zoeknaam = \'Exporteer ingrediënten per grondstof\' WHERE id = 152',

);


for($i = $tenant_start; $i <= $tenant_stop; $i++){
    $start = microtime(true);
    $db = 'actinum_studiov3_project_111_tenant'.$i;

    mysqli_select_db($link, $db);
    if (mysqli_errno($link)) {
        logMessage("MYSQL ERROR: ".mysqli_errno($link).' - '.mysqli_error($link)."\n");
        continue;
    }

    logMessage('Updating '.$db.'... ');
    try{
        mysqli_query_with_log($link, 'START TRANSACTION');

        foreach($queries as $query){
            mysqli_query($link, $query);
            if (mysqli_errno($link)) {
                logMessage("MYSQL ERROR: ".mysqli_errno($link).' - '.mysqli_error($link)."\n");
                logMessage("ON LINE ".$i.": ".$query);
            }
        }

        mysqli_query_with_log($link, 'COMMIT;');
    }
    catch(Exception $e){
        logMessage('Error: '.$e->getMessage());
        mysqli_query_with_log($link, 'ROLLBACK;');
    }
    $stop = microtime(true);
    $dur = round($stop - $start, 2);
    logMessage('DONE in '.$dur.'s.'."\n");
}

function mysqli_query_with_log($link, $query){
    $result = mysqli_query($link, $query);
    if (mysqli_errno($link)) {
        logMessage("MYSQL ERROR: ".mysqli_errno($link).' - '.mysqli_error($link)."\n");
        logMessage("  ==> query was: ".$query."\n");
    }
    return $result;
}

function logMessage($s){
    global $logfile;
    $handle = fopen($logfile, 'a');
    fwrite($handle,$s);
    echo $s;
    fclose($handle);
}
?>
