<?php
set_time_limit(0);

$logfile = '/tmp/migrations_12_'.date('Y-m-d_H-i-s').'.log';

$host = 'localhost';
$user = 'carenet_main';
$pass = ''; // fill in when running live

$tenant_start = 1;
$tenant_stop = 1;

$link = mysqli_connect($host, $user, $pass);

$queries = array(
    // execute always
    'SET NAMES utf8;',

    // queries from schema tool or custom queries
    'ALTER TABLE document_verifier ADD mail_send TINYINT( 1 ) NOT NULL DEFAULT  \'0\';',
);

for($i = $tenant_start; $i <= $tenant_stop; $i++){
    $start = microtime(true);
    $db = 'actinum_studiov3_project_111_tenant'.$i;

    mysqli_select_db($link, $db);
    if (mysqli_errno($link)) {
        logMessage("MYSQL ERROR: ".mysqli_errno($link).' - '.mysqli_error($link)."\n");
        continue;
    }

    logMessage('Updating '.$db.'... ');
    try{
        mysqli_query_with_log($link, 'START TRANSACTION');

        foreach($queries as $query){
            mysqli_query($link, $query);
            if (mysqli_errno($link)) {
                logMessage("MYSQL ERROR: ".mysqli_errno($link).' - '.mysqli_error($link)."\n");
                logMessage("ON LINE ".$i.": ".$query);
            }
        }

        mysqli_query_with_log($link, 'COMMIT;');
    }
    catch(Exception $e){
        logMessage('Error: '.$e->getMessage());
        mysqli_query_with_log($link, 'ROLLBACK;');
    }
    $stop = microtime(true);
    $dur = round($stop - $start, 2);
    logMessage('DONE in '.$dur.'s.'."\n");
}

function mysqli_query_with_log($link, $query){
    $result = mysqli_query($link, $query);
    if (mysqli_errno($link)) {
        logMessage("MYSQL ERROR: ".mysqli_errno($link).' - '.mysqli_error($link)."\n");
        logMessage("  ==> query was: ".$query."\n");
    }
    return $result;
}

function logMessage($s){
    global $logfile;
    $handle = fopen($logfile, 'a');
    fwrite($handle,$s);
    echo $s;
    fclose($handle);
}
?>
