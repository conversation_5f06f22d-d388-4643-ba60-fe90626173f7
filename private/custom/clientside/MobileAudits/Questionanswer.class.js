Actinum.Application.MobileAudits = Actinum.Application.MobileAudits || {};
Actinum.Application.MobileAudits.Questionanswer = new Class({

    question: null,
    answertypeobj: null,
    remarks: '',
    nonconformities: [],
    statusdiv: null,
    cameradiv: null,
    photos: [],

    initialize: function initialize(auditpart, data){
        // console.log('Questionanswer initialize: ', data);

        this.data = data;
        this.auditpart = auditpart;
        this.remarks = data.remarks;
        this.photos = data.photos || [];
        this.id = (data.id) ? data.id : "new_" + auditpart.audit.element.createGID();
        this.question = data.question;
        this.answertypeobj = new Actinum.Application.MobileAudits.AnswerTypes[data.answertype.jsclassname](this, data);
        data.nonconformities.each(function(nonconformity){
            this.nonconformities.push(new Actinum.Application.MobileAudits.Nonconformity(this, nonconformity));
        }.bind(this));
    },

    addNonconformity: function addNonconformity(nonconformity){
        this.nonconformities.push(nonconformity);
    },

    showAfwijkingPopup: function showAfwijkingPopup(item){
        //wordt een popup die ook gebruikt wordt voor het toevoegen van een actie.
        //bij het submitten wordt er aan this.answertypeobj gevraagd of het required is.
        //hij kan dan ook niet gedelete worden.

        var nc = null;
        var remark = '';
        var recurring = false;
        var freedropdown1 = null;
        var freedropdown2 = null;
        var photos = [];
        if (item){
            nc = item;
            remark = nc.remark;
            recurring = nc.recurring;
            freedropdown1 = nc.freedropdown1;
            freedropdown2 = nc.freedropdown2;

            if (item.photos) {
                photos = item.photos;
            }
        }

        $N('n10063n').setVraag(this.question.zoeknaam);

        $N('n10071n').setQuestionAnswer(this);

        $N('n10071n').setNonConformity(nc);

        // $N('n10066n').setDataAndNotify({'raw' : remark});
        // $N('n10068n').setDataAndNotify({'raw' : recurring});

        var data = $N('n28895n').getData();
        data.raw = freedropdown1;
        $N('n28895n').setDataAndNotify(data);

        var data = $N('n28898n').getData();
        data.raw = freedropdown2;
        $N('n28898n').setDataAndNotify(data);

        $N('n28903n').setPhotos(photos);

        $N('n10062n').showPopup();

        $N('n10066n').setDataAndNotify({'raw' : remark});
        $N('n10068n').setDataAndNotify({'raw' : recurring});


        $N('n10066n').element.focus();
    },

    showPhotoPopup: function showPhotoPopup(){
        $N('n10085n').setElementType('questionanswer');
        $N('n10085n').setQuestionAnswer(this);
        $N('n10080n').setVraag(this.question.zoeknaam);

        if ($N('n10061n').getPhotosenabled()){
            $N('n10079n').showPopup();
        }
        else{
            alert("Let op: het geheugen is vol. Er kunnen geen foto's meer genomen worden. Rond de inspectie af en synchroniseer.");
        }
    },

    showOpmerkingenPopup: function showOpmerkingenPopup(){
        $N('n10078n').setQuestionAnswer(this);
        $N('n10076n').setDataAndNotify({'raw' : this.remarks});
        $N('n10073n').setVraag(this.question.zoeknaam);
        $N('n10072n').showPopup();
        $N('n10076n').element.focus();
    },

    hide: function hide(){
        if (this.tr){
            this.tr.setStyle('display', 'none');
        }
    },

    show: function show(){
        if (this.tr){
            this.tr.setStyle('display', '');
        }
    },

    render: function render(){

        // render question mark if explanation is available.

        if ( this.question.explanation.length > 0 || this.question.explanation_image != null ) {
            var explanationqmark = new Element('td', {'style' : 'width: 5%;'}).adopt(
                new Element('a', {
                    'href' : 'javascript: void(0);',
                    'events' : {
                        'click' : this.showExplanation.bind(this)
                    }
                }).adopt(
                        new Element('img', {
                            'style' : 'width: 30px; height: 30px',
                            //'src' : 'assets/images/explanation.png'
                            'src' : 'data:image/png;base64,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'
                        })
                    )
            )
        } else {
            var explanationqmark = new Element('td', {'style' : 'width: 5%;'})
        }

        var tr = this.tr = new Element('tr');
        var statusdiv = this.statusdiv = this.renderStatusDiv();



        tr.adopt(
            new Element('td').adopt(
                statusdiv
            ),
            new Element('td', {'style' : 'width: 5%;'}).adopt(
                new Element('a', {
                    'href' : 'javascript: void(0);',
                    'events' : {
                        'click' : this.registerRemarks.bind(this)
                    }
                }).adopt(
                    new Element('img', {
                        'style' : 'width: 30px; height: 30px',
                        //'src' : 'assets/images/edit.png'
                        'src' : 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjEwMPRyoQAAAgtJREFUOE/F1Ltr2lEUB/BQurZTl/wRMRTpUCNaQrSDIIigNIOIEoh0KkQIDe3g+/1+REF8DHasopsOLsbJB2J9gY7Sli59mNbp9JwLCQ6F308biHC4DtfP73t+91x3du77s1qtHtxpBgQfz2az08FgcIX17L/xxWJxMZ/PYTgcQq/X+9zpdPa2QpfL5cNMJhPQaDQwnU5hDf2C6P7GaDabNSWTSTAYDKBWq2EymayjX9vt9lPeaDAYfBGPx3/hClarFfR6PWi1WhiPx7copuzwAgOBwGEikVgSZrPZwOPxsDIajaBSqWA0GjEU17ecYCgUOsJk1zcYrfgA8Hq9YLFYQKfTgUKhoKQWTiwSibzEd3ZNgN1uB4RZhcNh1jaV3++n9t9zYoVC4RVivwlzOByQTqdZ0aEQfoNh0necWDQa3a1Wq38IcDqdgKcL+XwecGQYTpjP56O2Lzgx2hCLxY5rtRr0+30olUpQLBYBE4PL5brF8FDOeWG0KZfLfWo2m+z08IpBpVIBt9vNMDoM/H7GGzObzQcymQwoYbfbZXNGVwxTE/YTU77hjdFGgUBwKRaL2YzV63VotVrQaDSgXC7/wFlUbIThCDwSCoXf5HI5UEqlUgnYfgvrNba5uxFGmyUSiQ5BkEql3xH8gAP73GQybf/fh3fzo0gkOsH3+GTjNP/6QSqV2j7NGvgXqhl5FdPM5SMAAAAASUVORK5CYII='
                    })
                )
            ),
            new Element('td', {'style' : 'width: 5%;'}).adopt(
                new Element('a', {
                    'href' : 'javascript: void(0);',
                    'events' : {
                        'click' : this.registerAfwijking.bind(this)
                    }
                }).adopt(
                    new Element('img', {
                        'style' : 'width: 30px; height: 30px',
                        //'src' : 'assets/images/exclamationmark.png'
                        'src' : 'data:image/png;base64,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'
                    })
                )
            ),
            new Element('td', {'style' : 'width: 5%;'}).adopt(
                new Element('a', {
                    'href' : 'javascript: void(0);',
                    'events' : {
                        'click' : this.registerPhotos.bind(this)
                    }
                }).adopt(
                    this.cameradiv = this.renderPhotoIcon()
                )
            ),
            new Element('td', {
                 'text' : this.question.zoeknaam,
                 'style' : 'top; width: 50%;'
            }),
            explanationqmark,
            new Element('td', {'style' : 'width: 40%;'}).adopt(
                this.answertypeobj.render(),
                this.renderPhotos(),
                this.renderRemarks(),
                this.renderNonconformities()
            )
        );
        return tr;
    },



    showExplanation: function showExplanation(){
        $N('n10087n').setExplanation(this.question.explanation, this.question.explanation_image);
        $N('n10086n').showPopup();
    },

    reRender: function reRender(){
        var oldtr = this.tr;
        var newtr = this.render();
        newtr.replaces(oldtr);
    },

    renderPhotoIcon: function renderPhotoIcon() {
        if(this.getPhotoRequired() === false) {
            return new Element('img', {
                'style': 'width: 30px; height: 30px',
                //'src' : 'assets/images/photocamera.png'
                'src': 'data:image/png;base64,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'
            })
        } else {
            return new Element('img', {
                'style': 'width: 38px; height: 20px',
                'src' : 'assets/images/photocamerarequired.png'
                // 'src': 'data:image/png;base64,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'
            })
        }
    },

    renderRemarks: function renderRemarks(){
        if (this.remarks){
            var div = new Element('div', {
                'class' : 'Infoarea',
                'text' : 'Opmerking: '+this.remarks
            });

            //deletelink
            var divdelete = new Element('div', {
                'style' : 'margin-right: 5px; width: 20px; height: 20px; float: left; cursor: pointer;',
                'events' : {
                    'click' : function(){
                        this.remarks = '';
                        this.reRender();
                    }.bind(this)
                }
            }).adopt(
                    new Element('img', {
                        //'src' : 'assets/images/delete.png'
                        'src' : 'data:image/png;base64,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'
                    })
                );
            divdelete.inject(div);

            //editlink
            var divEdit = new Element('div', {
                'style' : 'margin-right: 5px; width: 20px; height: 20px; float: left; cursor: pointer;',
                'events' : {
                    'click' : function(){
                        this.registerRemarks(this);
                    }.bind(this)
                }
            }).adopt(
                    new Element('img', {
                        //'src' : 'assets/images/edit.png'
                        'src' : 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjEwMPRyoQAAAgtJREFUOE/F1Ltr2lEUB/BQurZTl/wRMRTpUCNaQrSDIIigNIOIEoh0KkQIDe3g+/1+REF8DHasopsOLsbJB2J9gY7Sli59mNbp9JwLCQ6F308biHC4DtfP73t+91x3du77s1qtHtxpBgQfz2az08FgcIX17L/xxWJxMZ/PYTgcQq/X+9zpdPa2QpfL5cNMJhPQaDQwnU5hDf2C6P7GaDabNSWTSTAYDKBWq2EymayjX9vt9lPeaDAYfBGPx3/hClarFfR6PWi1WhiPx7copuzwAgOBwGEikVgSZrPZwOPxsDIajaBSqWA0GjEU17ecYCgUOsJk1zcYrfgA8Hq9YLFYQKfTgUKhoKQWTiwSibzEd3ZNgN1uB4RZhcNh1jaV3++n9t9zYoVC4RVivwlzOByQTqdZ0aEQfoNh0necWDQa3a1Wq38IcDqdgKcL+XwecGQYTpjP56O2Lzgx2hCLxY5rtRr0+30olUpQLBYBE4PL5brF8FDOeWG0KZfLfWo2m+z08IpBpVIBt9vNMDoM/H7GGzObzQcymQwoYbfbZXNGVwxTE/YTU77hjdFGgUBwKRaL2YzV63VotVrQaDSgXC7/wFlUbIThCDwSCoXf5HI5UEqlUgnYfgvrNba5uxFGmyUSiQ5BkEql3xH8gAP73GQybf/fh3fzo0gkOsH3+GTjNP/6QSqV2j7NGvgXqhl5FdPM5SMAAAAASUVORK5CYII='
                    })
                );
            divEdit.inject(div);
        }
        else{
            var div = new Element('div');
        }

//        div.addEvent('click', this.registerRemarks.bind(this));

        return div;
    },

    renderNonconformities: function renderNonconformities(){
        var container = new Element('div');

        //de nonconformities opsommen.
        this.nonconformities.each(function(item){
            var div = new Element('div', {
                'class' : 'Warningarea',
                'style' : 'overflow: auto;'
            });

            //deletelink
            var divdelete = new Element('div', {
                'style' : 'margin-right: 5px; width: 20px; height: 20px; float: left; cursor: pointer;',
                'events' : {
                    'click' : function(){
                        this.nonconformities.erase(item);
                        Stratcom.notify('auditanswerchanged');
                        this.reRender();
                    }.bind(this)
                }
            }).adopt(
                new Element('img', {
                    //'src' : 'assets/images/delete.png'
                    'src' : 'data:image/png;base64,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'
                })
            );
            divdelete.inject(div);

            // editlink
            var divEdit = new Element('div', {
                'style' : 'margin-right: 5px; width: 20px; height: 20px; float: left; cursor: pointer;',
                'events' : {
                    'click' : function(){
                        this.showAfwijkingPopup(item);
                    }.bind(this)
                }
            }).adopt(
                    new Element('img', {
                        //'src' : 'assets/images/edit.png'
                        'src' : 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjEwMPRyoQAAAgtJREFUOE/F1Ltr2lEUB/BQurZTl/wRMRTpUCNaQrSDIIigNIOIEoh0KkQIDe3g+/1+REF8DHasopsOLsbJB2J9gY7Sli59mNbp9JwLCQ6F308biHC4DtfP73t+91x3du77s1qtHtxpBgQfz2az08FgcIX17L/xxWJxMZ/PYTgcQq/X+9zpdPa2QpfL5cNMJhPQaDQwnU5hDf2C6P7GaDabNSWTSTAYDKBWq2EymayjX9vt9lPeaDAYfBGPx3/hClarFfR6PWi1WhiPx7copuzwAgOBwGEikVgSZrPZwOPxsDIajaBSqWA0GjEU17ecYCgUOsJk1zcYrfgA8Hq9YLFYQKfTgUKhoKQWTiwSibzEd3ZNgN1uB4RZhcNh1jaV3++n9t9zYoVC4RVivwlzOByQTqdZ0aEQfoNh0necWDQa3a1Wq38IcDqdgKcL+XwecGQYTpjP56O2Lzgx2hCLxY5rtRr0+30olUpQLBYBE4PL5brF8FDOeWG0KZfLfWo2m+z08IpBpVIBt9vNMDoM/H7GGzObzQcymQwoYbfbZXNGVwxTE/YTU77hjdFGgUBwKRaL2YzV63VotVrQaDSgXC7/wFlUbIThCDwSCoXf5HI5UEqlUgnYfgvrNba5uxFGmyUSiQ5BkEql3xH8gAP73GQybf/fh3fzo0gkOsH3+GTjNP/6QSqV2j7NGvgXqhl5FdPM5SMAAAAASUVORK5CYII='
                    })
                );
            divEdit.inject(div);

            if (item.recurring){
                //recurring symbooltje
                var img = new Element('img', {
                    'src' : 'data:image/png;base64,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',
                    'style' : 'margin-right: 10px;'
                });
                img.inject(div);
            }
            var textspan = new Element('span', {
                'text' : 'Afwijking: '+item.remark
            });
            textspan.inject(div);

            //div.addEvent('click', this.registerAfwijking.bind(item));

            div.inject(container);
        }.bind(this));

        return container;
    },

    getPhotos: function getPhotos(){
        return this.photos;
    },

    renderPhotos: function renderPhotos(){
        //de fotos
        var photocontainer = new Element('div', {
            'style' : 'margin-top: 10px;'
        });

        var nphotos = this.photos.length;
        if (nphotos > 0){
            var link = new Element('a', {
                'href' : 'javascript: void(0);',
                'style' : 'color: -webkit-link;',
                'text' : 'Bekijk '+ nphotos + ' ' + (nphotos == 1? 'foto' : 'foto\'s'),
                'events' : {
                    'click' : function(e){
                        this.showPhotoViewerPopup();
                    }.bind(this)
                }
            });
            link.inject(photocontainer)
        }

        return photocontainer;
    },

    showPhotoViewerPopup: function showPhotoViewerPopup(){
        $N('n10089n').setQuestionAnswer(this);
        $N('n10089n').reRender();
        $N('n10088n').showPopup();
    },

    isAnswered: function isAnswered(){
        return this.answertypeobj.isAnswered();
    },

    isValid: function isValid() {
        if (this.getPhotoRequired()) {
            if(this.photos.length === 0) {
                return false;
            }
        }

        return true;
    },

    handleChange: function handleChange(e){
        Stratcom.notify('auditanswerchanged');
        this.auditpart.updateStatusDiv();
        this.updateStatusDiv();
        this.updateCameraDiv();

        //we gaan de audit aanvullen met een 'inegvuld op datum/tijd'.
        //bij elke change handling wordt dit gewijzigd.
        this.auditpart.audit.setDateexecuted((new Date()).format('%d-%m-%Y %H:%M'));
    },

    renderStatusDiv: function renderStatusDiv(){
        let color = this.getStatus().color;
        if (this.answertypeobj.data.answertype.jsclassname === "Optionlist"
            && this.OptionListHasStandardAnswer(this.answertypeobj.answeroptionsIndexed) )
        {
            color = 'yellowgreen';
        }

        var statusdiv = new Element('div', {
            'style' : 'padding: 4px; color: white; font-weight: bold; width: 10px; height: 10px; border-radius: 4px; background-color: '+color+';'
        });
        return statusdiv;
    },

    OptionListHasStandardAnswer: function OptionListHasStandardAnswer(answerOptionsIndexed) {
        const indices = Object.keys(answerOptionsIndexed);
        const answerOptionsIndexedHasStandardAnswer =  answerOptionKey => answerOptionsIndexed[answerOptionKey].default_answer == true;
        return indices.some( answerOptionsIndexedHasStandardAnswer);
    },

    hasStandardAnswer: function hasStandardAnswer() {

    },

    updateStatusDiv: function updateStatusDiv(){
        var olddiv = this.statusdiv;
        var newdiv = this.renderStatusDiv();
        newdiv.replaces(olddiv);
        this.statusdiv = newdiv;
    },

    updateCameraDiv: function updateCameraDiv(){
        var olddiv = this.cameradiv;
        var newdiv = this.renderPhotoIcon();
        newdiv.replaces(olddiv);
        this.cameradiv = newdiv;
    },

    registerRemarks: function registerRemarks(){
        this.showOpmerkingenPopup();
    },

    registerAfwijking: function registerAfwijking(){
        this.showAfwijkingPopup();
    },

    registerPhotos: function registerPhotos(){
        this.showPhotoPopup();
    },

    getSubmitData: function getSubmitData(){
        //hier kunnen we dus mooi teruggeven wat we willen

        var questionanswer = this.answertypeobj.getSubmitData();
        questionanswer.id = this.id;
        questionanswer.remarks = this.remarks;
        questionanswer.photos = this.photos;
        questionanswer.nonconformities = [];

        this.nonconformities.each(function(item){
            questionanswer.nonconformities.push(item.getSubmitData());
        });

        return questionanswer;
    },

    getStatus: function getStatus(){
        if (this.isAnswered() && this.isValid()){
            var status = {
                'color' : 'yellowgreen',
                'text' : 'Beantwoord'
            }
        }
        else if (this.isAnswered()){
            var status = {
                'color' : '#ec6f2f',
                'text' : 'Bezig...'
            }
        }
        else{
            var status = {
                'color' : '#d6d6d6', //'#007ED5',
                'text' : 'Onbeantwoord'
            }
        }
        return status;
    },


    getPhotoRequired: function getPhotoRequired() {
        // als we al foto's hebben, altijd returnen dat het niet meer verplicht is
        if (this.photos.length > 0) {
            return false;
        }

        // als de vraag zelf vereist dat er een foto moet komen, dit returnen
        else if (this.question.photo_required === true) {
            return true;
        }

        else if (this.data.answertype.id == 1 && this.answertypeobj.answer != null && this.answertypeobj.answer != '') {

            var selectedoption = this.answertypeobj.answeroptionsIndexed[this.answertypeobj.answer+''];

            if (selectedoption.photo_required) {
                return true;
            }

            // else if (this.data.photo_required_nonconformity && selectedoption.actionrequired) {
            //     return true;
            // }

            else {
                return false;
            }
        }

        else {
            return false;
        }
    }

});
