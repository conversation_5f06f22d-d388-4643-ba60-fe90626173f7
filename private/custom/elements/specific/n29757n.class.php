<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29757n extends \nl\actinum\custom\elements\generic\Helppopup
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $tiptitle = NULL;
    public static $html = 'cursisten worden toegevoegd en aangemeld voor de cursistengroep(en) als het functieprofiel en/of de taal van de cursus overeenkomt met die van de cursistengroep';
    public static $helppopuptype = NULL;
    public static $style = '._this_ {
  margin-top: 8px;
  margin-bottom: 8px;
  float: right;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29757n';
    public static $parentElementid = 'n29753n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n29757n'] =
<<<'JS'
Actinum.Application.CustomElements.n29757n = new Class({ Extends: Actinum.Application.Elements.Helppopup,
    getMode: function getMode(){
        return 'add';
    },
});
JS;
        return $result;
    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29757n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>