<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28052n extends \nl\actinum\custom\elements\generic\Blockcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $additionalcssclasses = NULL;
    public static $style = '._this_ {
  width: 100%;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28052n';
    public static $parentElementid = 'n21132n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n21170n',
  1 => 'n21171n',
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n28052n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n28052n = new Class({ Extends: Actinum.Application.Elements.Blockcontainer,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28052n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>