<?php
namespace nl\actinum\custom\elements\specific {

/*[GENERATEDBYBUILDER

GENERATEDBYBUILDER]*/

class n5952n extends \nl\actinum\generated\elements\specific\n5952n    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n5952n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n5952n = new Class({ Extends: Actinum.Application.Elements.Gridcolumnyesno,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n5952n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>