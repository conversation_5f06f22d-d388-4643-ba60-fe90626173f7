<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29233n extends \nl\actinum\custom\elements\generic\Blockcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $additionalcssclasses = NULL;
    public static $style = '._this_ {
  width: 50%;
  padding-right: 20px;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29233n';
    public static $parentElementid = 'n29232n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n29234n',
  1 => 'n29235n',
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29233n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29233n = new Class({ Extends: Actinum.Application.Elements.Blockcontainer,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29233n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>