<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28520n extends \nl\actinum\custom\elements\generic\Doctrineorderby
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $orderbystring = 'language.zoeknaam';
    public static $orderbyascdesc = 'asc';
    public static $orderbydescription = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28520n';
    public static $parentElementid = 'n28519n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n28520n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n28520n = new Class({ Extends: Actinum.Application.Elements.Doctrineorderby,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28520n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>