<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29103n extends \nl\actinum\custom\elements\generic\Forminputstring
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $subtitle = 'Onderwerp';
    public static $showsubtitle = false;
    public static $dbfield = 'inspection_certificate_subject';
    public static $defaultvalue = '';
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = false;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29103n';
    public static $parentElementid = 'n29101n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29103n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29103n = new Class({ Extends: Actinum.Application.Elements.Forminputstring,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29103n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>