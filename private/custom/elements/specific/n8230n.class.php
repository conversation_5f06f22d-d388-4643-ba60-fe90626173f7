<?php
namespace nl\actinum\custom\elements\specific {

/*[GENERATEDBYBUILDER

GENERATEDBYBUILDER]*/

class n8230n extends \nl\actinum\generated\elements\specific\n8230n    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n8230n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n8230n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainerlabel,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n8230n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>