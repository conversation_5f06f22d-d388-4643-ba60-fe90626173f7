<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28551n extends \nl\actinum\custom\elements\generic\Forminputtext
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $subtitle = 'Opmerking';
    public static $showsubtitle = false;
    public static $dbfield = 'remark';
    public static $defaultvalue = '';
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = true;
    public static $style = '._this_ {
  width: 400px;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28551n';
    public static $parentElementid = 'n28549n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n28551n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n28551n = new Class({ Extends: Actinum.Application.Elements.Forminputtext,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28551n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>