<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28909n extends \nl\actinum\custom\elements\generic\Forminputstring
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $subtitle = NULL;
    public static $showsubtitle = NULL;
    public static $dbfield = 'vrijdropdown1naaminspectieafwijkingen';
    public static $defaultvalue = '';
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28909n';
    public static $parentElementid = 'n28906n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n28909n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n28909n = new Class({ Extends: Actinum.Application.Elements.Forminputstring,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28909n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>