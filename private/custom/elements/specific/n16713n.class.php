<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n16713n extends \nl\actinum\custom\elements\generic\Forminputcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n16713n';
    public static $parentElementid = 'n16710n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n16714n',
);
/*GENERATEDBYBUILDER]*/

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n16713n'] =
<<<'JS'
Actinum.Application.CustomElements.n16713n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainer,

    allowAutomaticHiding: function allowAutomaticHiding(){
        return false;
    }

});
JS;
        return $result;
    }



    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.CustomCss.n16713n'] =
<<<'CSS'
CSS;
        return $result;
    }


        


}

}
?>