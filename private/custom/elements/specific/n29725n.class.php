<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29725n extends \nl\actinum\custom\elements\generic\Forminputcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29725n';
    public static $parentElementid = 'n17803n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n29726n',
);
/*GENERATEDBYBUILDER]*/

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n29725n'] =
<<<'JS'
Actinum.Application.CustomElements.n29725n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainer,
allowAutomaticHiding: function allowAutomaticHiding(){
        return false;
    }
});
JS;
        return $result;
    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29725n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>