<?php
namespace nl\actinum\custom\elements\specific {




/*[GENERATEDBYBUILDER_CLASSNAME*/
class n10290n extends \nl\actinum\custom\elements\generic\Userpreferencemodule
/*GENERATEDBYBUILDER_CLASSNAME]*/
    implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode

                                                                                                                            {
/*[GENERATEDBYBUILDER*/

    public static $title = 'Mijn gegevens';
    public static $elementid = 'n10290n';
    public static $parentElementid = 'n2n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n10291n',
);
/*GENERATEDBYBUILDER]*/


    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n10290n'] =
<<<'JS'
Actinum.Application.CustomElements.n10290n = new Class({ Extends: Actinum.Application.Elements.Subpage,
    registerListeners: function registerListeners(){
        this.parent();
        this.addListeners(['renderingdone'],  this.updateVisibility.bind(this));
    },

    updateVisibility: function updateVisibility(){
        console.log('data', this.getData())
        // Check if user should see this module based on profile 140
        if (this.getData().shouldRender){
            this.showDomcontent();
        }
        else{
            this.hideDomcontent();
        }
    }
});
JS;
        return $result;
    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n10290n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        protected static function getDataForAll(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings)
        {
            echo '<pre>';
            echo var_dump(12313);
            echo '</pre>';
            die();
//            $em = static::getEM();
//            $user = \nl\actinum\framework\application\Registry::get('login')->getUser();
//
//            // Check if user has profile 140
//            $requiredProfile = $em->find('\nl\actinum\custom\tables\profile', 140);
//            $data['shouldRender'] = $user->hasProfile($requiredProfile);
//
//            return $data;
        }


}

}
?>