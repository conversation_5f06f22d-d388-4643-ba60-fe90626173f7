<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n15752n extends \nl\actinum\custom\elements\generic\Gridcolumnint
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $dbfield = 'number';
    public static $addlink = NULL;
    public static $disabledadvancedsearch = NULL;
    public static $title = 'Nummer';
    public static $show = true;
    public static $dbtrigger = NULL;
    public static $mappedforminput = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n15752n';
    public static $parentElementid = 'n15737n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYB<PERSON>LDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n15752n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n15752n = new Class({ Extends: Actinum.Application.Elements.Gridcolumnint,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n15752n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>