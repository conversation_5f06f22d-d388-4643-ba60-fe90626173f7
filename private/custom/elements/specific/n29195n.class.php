<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29195n extends \nl\actinum\custom\elements\generic\Forminputyesno
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $subtitle = 'Zichtbaar';
    public static $showsubtitle = true;
    public static $dbfield = 'vrijdropdown1tonenacties';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29195n';
    public static $parentElementid = 'n29193n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29195n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29195n = new Class({ Extends: Actinum.Application.Elements.Forminputyesno,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29195n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>