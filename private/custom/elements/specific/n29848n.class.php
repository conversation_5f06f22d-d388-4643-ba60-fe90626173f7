<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29848n extends \nl\actinum\custom\elements\generic\Doctrinefilter
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $filterstring = '1=1';
    public static $filterdescription = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29848n';
    public static $parentElementid = 'n5424n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29848n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29848n = new Class({ Extends: Actinum.Application.Elements.Forminputsearch,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29848n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }

        public static function getDoctrineFilters(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $filters = parent::getDoctrineFilters($drw, $settings);

            $employee = \nl\actinum\framework\application\Registry::get('login')->getUser();
            $profile = static::getEM()->find('nl\actinum\custom\tables\profile', 210);
            
            if ($employee->hasProfile($profile)){
                $filters[] = new \nl\actinum\framework\application\DoctrineFilter(
                    sprintf(
                        'department.id IN (%s)',
                        implode(',', $employee->getDepartmentIdsSharedByLocations())
                    ),
                    array()
                );
            }

            $filters[] = new \nl\actinum\framework\application\DoctrineFilter(
                'department.active = TRUE',
                array()
            );

            return $filters;
        }
        


}

}
?>