<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n27467n extends \nl\actinum\custom\elements\generic\Buttoncustom
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'Updaten';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n27467n';
    public static $parentElementid = 'n27466n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = false;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

   public static function getJs(){
       $result = parent::getJs();
       $result['Actinum.Application.CustomJs.n27467n'] =
<<<'JS'
Actinum.Application.CustomElements.n27467n = new Class({ Extends: Actinum.Application.Elements.Buttoncustom,
        execute: function execute(){
        //alle settings zijn al goed per element dat settings nodig heeft.
        //hoeven dus alleen de blockcontainer te uppen.
        Actinum.application.updateElements([
            {
                'element' : $N('n3302n'),
            }
        ]);

    }
});
JS;
       return $result;
   }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n27467n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>