<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28706n extends \nl\actinum\custom\elements\generic\Forminputcontainerlabel
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'Deadline automatisch updaten op basis van verloopdatum handboekdocument';
    public static $style = '._this_ {
  width: auto;
  font-weight: bold;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28706n';
    public static $parentElementid = 'n28703n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n28706n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n28706n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainerlabel,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28706n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>