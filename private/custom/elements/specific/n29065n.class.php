<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29065n extends \nl\actinum\custom\elements\generic\Forminputsearchmultiple
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $tablename = 'document';
    public static $selectdql = 'document.zoeknaam';
    public static $aliaspostfix = NULL;
    public static $min = '0';
    public static $max = '0';
    public static $detailpage = NULL;
    public static $rowtypetopagemapping = 'null';
    public static $allownondatabaseentries = NULL;
    public static $createdatabaseentrieswhennotexists = NULL;
    public static $jointablename = NULL;
    public static $findingridpopupblock = 'n29067n';
    public static $subtitle = NULL;
    public static $showsubtitle = NULL;
    public static $dbfield = 'document';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = '._this_ {
  width: 280px;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29065n';
    public static $parentElementid = 'n29063n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n29066n',
);
/*GENERATEDBYBUILDER]*/

   public static function getJs(){
       $result = parent::getJs();
       $result['Actinum.Application.CustomJs.n29065n'] =
<<<'JS'
Actinum.Application.CustomElements.n29065n = new Class({ Extends: Actinum.Application.Elements.Forminputsearchmultiple,
    getMode: function getMode(){
        return 'add';
    }
});
JS;
       return $result;
   }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29065n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }

        public static function convertSettingToSqlFunctionParameterValue($documents){
            if (count($documents) > 0) {
                return implode(", ", $documents);
            }
            return '';
        }



}

}
?>
