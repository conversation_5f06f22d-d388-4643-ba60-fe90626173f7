<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29124n extends \nl\actinum\custom\elements\generic\Forminputcontainerlabel
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'Tenant toont ID’s van de vragen in vragenlijsten onder tabblad Inspectieronden';
    public static $style = '._this_ {
  width: initial;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29124n';
    public static $parentElementid = 'n29122n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29124n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29124n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainerlabel,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29124n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>