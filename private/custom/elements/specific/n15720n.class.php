<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n15720n extends \nl\actinum\custom\elements\generic\Blockcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $additionalcssclasses = NULL;
    public static $style = '._this_ {
  width: 25%;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n15720n';
    public static $parentElementid = 'n15657n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n15721n',
  1 => 'n15722n',
  2 => 'n15725n',
  3 => 'n15729n',
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n15720n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n15720n = new Class({ Extends: Actinum.Application.Elements.Blockcontainer,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n15720n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>