<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n12501n extends \nl\actinum\custom\elements\generic\Subpagelink
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $subpage = 'n11810n';
    public static $title = 'Grondstof';
    public static $subtitle = '';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n12501n';
    public static $parentElementid = 'n12357n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n12501n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n12501n = new Class({ Extends: Actinum.Application.Elements.Subpagelink,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n12501n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>