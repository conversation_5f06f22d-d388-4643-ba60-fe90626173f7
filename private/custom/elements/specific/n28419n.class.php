<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28419n extends \nl\actinum\custom\elements\generic\Forminputcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28419n';
    public static $parentElementid = 'n9273n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n28420n',
  1 => 'n28421n',
  2 => 'n29611n',
);
/*GENERATEDBYBUILDER]*/

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n28419n'] =
<<<'JS'
Actinum.Application.CustomElements.n28419n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainer,

    registerListeners: function() {
        this.parent();
        this.addListeners(['renderingdone'], this.updateVisibility.bind(this));
    },

    updateVisibility: function(){
        var data = $N('n28420n').getData();
        if(data.labeltonen) {
            this.showDomcontent();
        } else {
            this.hideDomcontent();
        }
    }

});
JS;
        return $result;
    }



//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28419n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>