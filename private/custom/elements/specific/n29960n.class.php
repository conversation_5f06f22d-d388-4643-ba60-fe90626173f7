<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29960n extends \nl\actinum\custom\elements\generic\Buttonsubmitgridrowform
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'Toevoegen';
    public static $edittitle = 'Klaar';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29960n';
    public static $parentElementid = 'n29957n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = false;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29960n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29960n = new Class({ Extends: Actinum.Application.Elements.Buttonsubmitgridrowform,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29960n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>