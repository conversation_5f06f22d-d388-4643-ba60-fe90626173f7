<?php
namespace nl\actinum\custom\elements\specific {



class n9838n extends \nl\actinum\custom\elements\generic\Subpagelink    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {
/*[GENERATEDBYBUILDER*/

    public static $subpage = 'n9432n';
    public static $title = 'Productspecificaties';
    public static $subtitle = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n9838n';
    public static $parentElementid = 'n9449n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n9838n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n9838n = new Class({ Extends: Actinum.Application.Elements.Subpagelink,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n9838n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>