<?php
namespace nl\actinum\custom\elements\specific {




/*[GENERATEDBYBUILDER_CLASSNAME*/
class n5390n extends \nl\actinum\custom\elements\generic\Forminputdropdown
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {
/*[GENERATEDBYBUILDER*/

    public static $tablename = 'department';
    public static $selectdql = 'department_action.zoeknaam';
    public static $aliaspostfix = '';
    public static $detailpage = NULL;
    public static $rowtypetopagemapping = 'null';
    public static $subtitle = 'Afdeling';
    public static $showsubtitle = true;
    public static $dbfield = 'department_action';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n5390n';
    public static $parentElementid = 'n5388n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/


//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n5390n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n5390n = new Class({ Extends: Actinum.Application.Elements.Forminputdropdown,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n5390n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>