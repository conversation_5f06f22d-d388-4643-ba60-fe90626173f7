<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29158n extends \nl\actinum\custom\elements\generic\Blockpopup
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'Afdeling opzoeken...';
    public static $style = '._this_ {
  width: 700px;
  z-index: 21 !important;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29158n';
    public static $parentElementid = 'n3712n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n29159n',
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29158n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29158n = new Class({ Extends: Actinum.Application.Elements.Blockpopup,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29158n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>