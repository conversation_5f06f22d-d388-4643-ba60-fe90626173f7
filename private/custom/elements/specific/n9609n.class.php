<?php
namespace nl\actinum\custom\elements\specific {




/*[GENERATEDBYBUILDER_CLASSNAME*/
class n9609n extends \nl\actinum\custom\elements\generic\Gridpreset
/*GENERATEDBYBUILDER_CLASSNAME]*/
  implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {
/*[GENERATEDBYBUILDER*/

    public static $title = 'Alle';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n9609n';
    public static $parentElementid = 'n9608n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n9610n',
);
/*GENERATEDBYBUILDER]*/


    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n9609n'] =
<<<'JS'
Actinum.Application.CustomElements.n9609n = new Class({ Extends: Actinum.Application.Elements.Gridpreset,
});
JS;
        return $result;
    }



    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.CustomCss.n9609n'] =
<<<'CSS'
.n9609n{
    margin-left: 15px;
}
CSS;
        return $result;
    }


        


}

}
?>