<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n16593n extends \nl\actinum\custom\elements\generic\Gridcolumnsearchmultiple
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $tablename = 'employee';
    public static $selectdql = 'employee_verifier.zoeknaam';
    public static $aliaspostfix = '';
    public static $dbfield = 'employee_verifier';
    public static $addlink = false;
    public static $disabledadvancedsearch = NULL;
    public static $title = 'Verificateur';
    public static $show = true;
    public static $dbtrigger = NULL;
    public static $mappedforminput = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n16593n';
    public static $parentElementid = 'n16426n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n16593n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n16593n = new Class({ Extends: Actinum.Application.Elements.Gridcolumnsearchmultiple,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n16593n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>