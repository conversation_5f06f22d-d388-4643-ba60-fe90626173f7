<?php
namespace nl\actinum\custom\elements\specific {




/*[GENERATEDBYBUILDER_CLASSNAME*/
class n4227n extends \nl\actinum\custom\elements\generic\Forminputdropdown
/*GENERATEDBYBUILDER_CLASSNAME]*/
  implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {
/*[GENERATEDBYBUILDER*/

    public static $tablename = 'questionnaire';
    public static $selectdql = 'questionnaire.zoeknaam';
    public static $aliaspostfix = NULL;
    public static $detailpage = NULL;
    public static $rowtypetopagemapping = 'null';
    public static $subtitle = 'Inspectie';
    public static $showsubtitle = false;
    public static $dbfield = 'questionnaire';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = true;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n4227n';
    public static $parentElementid = 'n4225n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n6531n',
);
/*GENERATEDBYBUILDER]*/


    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n4227n'] =
<<<'JS'
Actinum.Application.CustomElements.n4227n = new Class({ Extends: Actinum.Application.Elements.Forminputdropdown,

    getMode: function getMode(){
        return 'add';
    }

});
JS;
        return $result;
    }



    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.CustomCss.n4227n'] =
<<<'CSS'
CSS;
        return $result;
    }

    public static function getDataForView(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
        return parent::getDataForAdd($drw, $settings);
    }


}

}
?>