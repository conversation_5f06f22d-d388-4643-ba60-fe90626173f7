<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28742n extends \nl\actinum\custom\elements\generic\Doctrinefilter
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $filterstring = '1=1';
    public static $filterdescription = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28742n';
    public static $parentElementid = 'n20246n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n28742n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n28742n = new Class({ Extends: Actinum.Application.Elements.Doctrinefilter,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28742n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }

        public static function getDoctrineFilters(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $filters = parent::getDoctrineFilters($drw, $settings);

            $filterstring = 'department.active = TRUE';
            $parameters = array();
            $doctrineFilter = new \nl\actinum\framework\application\DoctrineFilter($filterstring, $parameters);
            $filters[] = $doctrineFilter;

            return $filters;
        }



}

}
?>