<?php
namespace nl\actinum\custom\elements\specific {




/*[GENERATEDBYBUILDER_CLASSNAME*/
class n9440n extends \nl\actinum\custom\elements\generic\Gridaddoncontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {
/*[GENERATEDBYBUILDER*/

    public static $grid = 'n9439n';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n9440n';
    public static $parentElementid = 'n9439n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n9441n',
  1 => 'n9443n',
);
/*GENERATEDBYBUILDER]*/


//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n9440n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n9440n = new Class({ Extends: Actinum.Application.Elements.Gridaddoncontainer,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n9440n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>