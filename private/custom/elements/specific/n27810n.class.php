<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n27810n extends \nl\actinum\custom\elements\generic\Forminputcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n27810n';
    public static $parentElementid = 'n27802n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n27811n',
);
/*GENERATEDBYBUILDER]*/

   public static function getJs(){
       $result = parent::getJs();
       $result['Actinum.Application.CustomJs.n27810n'] =
<<<'JS'
Actinum.Application.CustomElements.n27810n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainer,
    allowAutomaticHiding: function allowAutomaticHiding() {
        return false;
    }
});
JS;
       return $result;
   }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n27810n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>