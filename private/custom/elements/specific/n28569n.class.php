<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28569n extends \nl\actinum\custom\elements\generic\Header1
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'Documenten';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28569n';
    public static $parentElementid = 'n10155n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n28569n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n28569n = new Class({ Extends: Actinum.Application.Elements.Header1,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28569n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>