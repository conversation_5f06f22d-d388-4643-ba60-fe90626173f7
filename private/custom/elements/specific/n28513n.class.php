<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28513n extends \nl\actinum\custom\elements\generic\Form
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
    implements \nl\actinum\framework\application\interfaces\IHasDataForView,
    \nl\actinum\framework\application\interfaces\IHasDataForAdd,
    \nl\actinum\framework\application\interfaces\IHasDataForEdit
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28513n';
    public static $parentElementid = 'n28512n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n28514n',
  1 => 'n28516n',
  2 => 'n28518n',
  3 => 'n28521n',
);
/*GENERATEDBYBUILDER]*/

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n28513n'] =
<<<'JS'
Actinum.Application.CustomElements.n28513n = new Class({ Extends: Actinum.Application.Elements.Form,
    
    render: function render(){
        if (this.getData().inspection_labels === true){
            return this.parent.apply(this, arguments);
        }
        return null;
    },
    
    registerListeners: function registerListeners(){
        this.parent.apply(this, arguments);
        $N('n2n').addUpdateInspectionPlanningLabelsSettingsListeners(this);
    }

});
JS;
        return $result;
    }



//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28513n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }

        public static function getDataForAdd(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $ssdrw = \nl\actinum\framework\custom\application\Application::getDefaultSystemSettings();
            $ssarr = $ssdrw->getArray();

            $data['inspection_labels'] = $ssarr['inspection_labels'];

            return $data;
        }
        public static function getDataForView(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $ssdrw = \nl\actinum\framework\custom\application\Application::getDefaultSystemSettings();
            $ssarr = $ssdrw->getArray();

            $data['inspection_labels'] = $ssarr['inspection_labels'];

            return $data;
        }

        public static function getDataForEdit(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $ssdrw = \nl\actinum\framework\custom\application\Application::getDefaultSystemSettings();
            $ssarr = $ssdrw->getArray();

            $data['inspection_labels'] = $ssarr['inspection_labels'];

            return $data;
        }
        


}

}
?>