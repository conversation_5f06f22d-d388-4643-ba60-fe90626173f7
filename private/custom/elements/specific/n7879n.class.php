<?php
namespace nl\actinum\custom\elements\specific {

/*[GENERATEDBYBUILDER

GENERATEDBYBUILDER]*/

class n7879n extends \nl\actinum\generated\elements\specific\n7879n implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

    public static function getJsMobile(){
        $result = parent::getJsMobile();
        $result['Actinum.Application.CustomJs.n7879n'] =
<<<'JS'
Actinum.Application.CustomElements.n7879n = new Class({ Extends: Actinum.Application.Elements.Buttoncustom,

    questionanswer: null,

    execute: function execute(){
        if ($N('n7862n').getData().raw){
            var nonconformitydata = {
                'remark' : $N('n7862n').getData().raw,
                'recurring' : $N('n7893n').getData().raw
            };
            var nonconformity = new Actinum.Application.MobileAudits.Nonconformity(this.questionanswer, nonconformitydata);
            this.questionanswer.addNonconformity(nonconformity);
            this.questionanswer.reRender();
            Stratcom.notify('auditanswerchanged');
        }
        $N('n7846n').hidePopup();
    },

    setQuestionAnswer: function setQuestionAnswer(questionanswer){
        this.questionanswer = questionanswer;
    },

    render: function render(){
        var el = this.parent();

        this.button.inject($N('n7846n').privates.domcontent, 'top');
        this.button.setStyles({
            'float' : 'right',
            'margin-left' : '20px'
        });

        return el;
    }

});
JS;
        return $result;
    }



    public static function getCssMobile(){
        $result = parent::getCssMobile();
        $result['Actinum.Application.CustomCss.n7879n'] =
<<<'CSS'
CSS;
        return $result;
    }


        


}

}
?>