<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n15821n extends \nl\actinum\custom\elements\generic\Forminputcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n15821n';
    public static $parentElementid = 'n14818n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n15822n',
  1 => 'n15823n',
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n15821n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n15821n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainer,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n15821n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>