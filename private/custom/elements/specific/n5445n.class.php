<?php
namespace nl\actinum\custom\elements\specific {

/*[GENERATEDBYBUILDER

GENERATEDBYBUILDER]*/

class n5445n extends \nl\actinum\generated\elements\specific\n5445n    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n5445n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n5445n = new Class({ Extends: Actinum.Application.Elements.Doctrinefilter,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n5445n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        public static function getDoctrineFilters(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $filters = parent::getDoctrineFilters($drw, $settings);
            $filters = array_merge($filters, \nl\actinum\custom\tables\employee::getDepartmentFilters($drw, $settings, 'employee_verification', 'department_verification'));
            return $filters;
        }


}

}
?>