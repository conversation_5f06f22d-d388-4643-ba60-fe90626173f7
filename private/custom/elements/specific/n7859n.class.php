<?php
namespace nl\actinum\custom\elements\specific {

/*[GENERATEDBYBUILDER

GENERATEDBYBUILDER]*/

class n7859n extends \nl\actinum\generated\elements\specific\n7859n  implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

    public static function getJsMobile(){
        $result = parent::getJsMobile();
        $result['Actinum.Application.CustomJs.n7859n'] =
<<<'JS'
Actinum.Application.CustomElements.n7859n = new Class({ Extends: Actinum.Application.Elements.Form
});
JS;
        return $result;
    }



    public static function getCssMobile(){
        $result = parent::getCssMobile();
        $result['Actinum.Application.CustomCss.n7859n'] =
<<<'CSS'
.n7859n{
overflow: visible;
}
CSS;
        return $result;
    }


        


}

}
?>