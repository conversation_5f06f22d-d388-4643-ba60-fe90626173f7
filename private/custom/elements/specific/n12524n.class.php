<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n12524n extends \nl\actinum\custom\elements\generic\Subpagelink
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $subpage = 'n12239n';
    public static $title = 'Afnemers';
    public static $subtitle = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n12524n';
    public static $parentElementid = 'n12416n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n12524n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n12524n = new Class({ Extends: Actinum.Application.Elements.Subpagelink,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n12524n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>