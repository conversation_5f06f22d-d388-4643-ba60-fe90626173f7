<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29552n extends \nl\actinum\custom\elements\generic\Gridcolumnsearch
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $tablename = 'course';
    public static $selectdql = 'created_course.zoeknaam';
    public static $aliaspostfix = '';
    public static $dbfield = 'created_course';
    public static $addlink = NULL;
    public static $disabledadvancedsearch = NULL;
    public static $title = 'created_course';
    public static $show = false;
    public static $dbtrigger = NULL;
    public static $mappedforminput = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29552n';
    public static $parentElementid = 'n27508n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29552n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29552n = new Class({ Extends: Actinum.Application.Elements.Gridcolumnsearch,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29552n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }

//        public static function getSubqueryDqls(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings)
//        {
//            $result = parent::getSubqueryDqls($drw, $settings);
//            $result[] = array(
//                'subquery' => 'created_course.zoeknaam',
//                'alias' => 'created_course_zoeknaam'
//            );
//            return $result;
//        }

    public static function getSubqueryDqls(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings)
    {
        $result = parent::getSubqueryDqls($drw, $settings);
        $result[] = array(
            'subquery' => 'created_course.zoeknaam',
            'alias' => 'created_course_zoeknaam'
        );
        return $result;
    }

}

}
?>
