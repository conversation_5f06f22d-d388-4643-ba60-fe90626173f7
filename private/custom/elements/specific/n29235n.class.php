<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29235n extends \nl\actinum\custom\elements\generic\Plusbuttongrid
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $detailpage = NULL;
    public static $rowtypetopagemapping = 'null';
    public static $dbfield = 'vrijveld3dropdownacties';
    public static $tablename = 'vrijveld3dropdownacties';
    public static $rowsdraggable = true;
    public static $removelinkcaption = 'Remove';
    public static $addlinkcaption = 'Add';
    public static $min = '1';
    public static $max = '0';
    public static $defaultentries = 'null';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29235n';
    public static $parentElementid = 'n29233n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
  0 => 'n29236n',
  1 => 'n29237n',
  2 => 'n29242n',
  3 => 'n29243n',
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29235n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29235n = new Class({ Extends: Actinum.Application.Elements.Plusbuttongrid,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29235n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>