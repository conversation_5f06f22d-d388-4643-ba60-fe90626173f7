<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n12332n extends \nl\actinum\custom\elements\generic\Subpagelink
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $subpage = 'n11732n';
    public static $title = 'Grondstof';
    public static $subtitle = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n12332n';
    public static $parentElementid = 'n12331n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n12332n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n12332n = new Class({ Extends: Actinum.Application.Elements.Subpagelink,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n12332n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>