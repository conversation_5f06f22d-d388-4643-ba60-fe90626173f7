<?php
namespace nl\actinum\custom\elements\specific {

/*[GENERATEDBYBUILDER

GENERATEDBYBUILDER]*/

class n6273n extends \nl\actinum\generated\elements\specific\n6273n    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n6273n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n6273n = new Class({ Extends: Actinum.Application.Elements.Gridcolumnstring,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n6273n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>