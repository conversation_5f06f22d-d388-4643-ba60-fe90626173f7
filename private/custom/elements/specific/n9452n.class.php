<?php
namespace nl\actinum\custom\elements\specific {




/*[GENERATEDBYBUILDER_CLASSNAME*/
class n9452n extends \nl\actinum\custom\elements\generic\Gridcolumnint
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {
/*[GENERATEDBYBUILDER*/

    public static $dbfield = 'year';
    public static $addlink = false;
    public static $disabledadvancedsearch = NULL;
    public static $title = 'Jaar';
    public static $show = true;
    public static $dbtrigger = NULL;
    public static $mappedforminput = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n9452n';
    public static $parentElementid = 'n9439n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/


//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n9452n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n9452n = new Class({ Extends: Actinum.Application.Elements.Gridcolumnint,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n9452n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>