<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n29157n extends \nl\actinum\custom\elements\generic\Doctrineorderby
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $orderbystring = 'department.name';
    public static $orderbyascdesc = 'asc';
    public static $orderbydescription = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n29157n';
    public static $parentElementid = 'n29151n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n29157n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n29157n = new Class({ Extends: Actinum.Application.Elements.Doctrineorderby,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n29157n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>