<?php
namespace nl\actinum\custom\elements\specific {

/*[GENERATEDBYBUILDER

GENERATEDBYBUILDER]*/

class n6729n extends \nl\actinum\generated\elements\specific\n6729n    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n6729n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n6729n = new Class({ Extends: Actinum.Application.Elements.Blockpopup,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n6729n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>