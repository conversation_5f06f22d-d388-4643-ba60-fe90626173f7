<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n28830n extends \nl\actinum\custom\elements\generic\Helppopup
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $tiptitle = NULL;
    public static $html = NULL;
    public static $helppopuptype = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n28830n';
    public static $parentElementid = 'n28827n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n28830n'] =
<<<'JS'
Actinum.Application.CustomElements.n28830n = new Class({ Extends: Actinum.Application.Elements.Helppopup,

    getToolTipContent: function getToolTipContent(){
        var container = new Element('div', {
            'style' : 'width: 300px'
        });
        var title = new Element('div', {
            'class' : 'Title',
            'text' : this.getProperty('tiptitle') ? this.getProperty('tiptitle') : 'Uitleg'
        });
        var content = new Element('div', {
            'class' : 'Content'
        });
        var regel1 = new Element('div', {
            'text' : 'De volgende velden worden vervangen:',
            'style' : 'margin-bottom: 10px;'
        });
        regel1.inject(content);

        var replacements = this.getReplacements();

        replacements.each(function(replacement){
            var regel = new Element('a', {
                'text' : replacement,
                'style' : 'display: block; color: #9FD4FF !important;',
                'href' : 'javascript: void(0);',
                'events' : {
                    'click' : function(e){
                        this.injectReplacement(replacement);
                    }.bind(this)
                }
            });
            regel.inject(content);  
        }.bind(this));


        title.inject(container);
        content.inject(container);
        return container;
    },

    getReplacements: function getReplacements(){
        var result = ['{verlooptbinnen}', '{cursus}', '{cursistengroep}', '{cursist}'];
        return result;
    },

    injectReplacement: function injectReplacement(replacement){
        $N('n28829n').element.insertAtCursor(replacement, false);
        $N('n28829n').element.fireEvent('change', {'target' : $N('n28829n').element}); //zelfde als gebeurt als we het zelf getypt hadden.
    }

});
JS;
        return $result;
    }



//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n28830n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }





}

}
?>